---
type: "always_apply"
description: "Frontend development workflow with Vue/React/TypeScript - 前端开发完整工作流"
---
# 🎨 Frontend Development Workflow - 前端开发工作流

专为 Vue 3、React 18+、TypeScript 优化的前端开发完整工作流程。

## 🚀 Commands - 命令

- `/frontend-dev` - 启动前端开发工作流
- `/component` - 创建新组件
- `/ui-design` - UI设计和样式开发
- `/test-frontend` - 运行前端测试
- `/build-frontend` - 构建和优化

### 前端专用配置模式
- `/设置Vue优先模式` - 优先使用 Vue 3 技术栈
- `/设置React优先模式` - 优先使用 React 18+ 技术栈
- `/设置TypeScript严格模式` - 启用最严格的类型检查
- `/设置性能优化模式` - 自动应用前端性能优化建议

### 反馈频率控制
- `/设置详细模式` - 启用所有反馈点，完整工作流
- `/设置标准模式` - 关键决策点反馈（默认）
- `/设置静默模式` - 仅错误时反馈，适合熟练用户

### 工作流配置
- `/设置严格模式` - 严格按顺序执行，不允许跳过
- `/设置灵活模式` - 允许模式跳转和流程调整（默认）
- `/设置快捷模式` - 简化某些步骤，提高效率

### 质量标准配置
- `/设置企业级标准` - 最高质量要求，完整测试
- `/设置标准级别` - 平衡质量和效率（默认）
- `/设置原型级别` - 快速验证，降低质量要求

## 📋 Process - 开发流程

### 1. 🔍 需求分析 (Requirements Analysis)
- 分析用户交互需求和业务逻辑
- 确定技术栈和架构方案
- 设计组件结构和数据流
- **转换条件**：需求明确，技术方案确定

### 2. 🎨 UI设计 (UI Design)
- 设计组件界面和交互逻辑
- 确定样式规范和响应式布局
- 规划路由和页面结构
- **转换条件**：UI设计完成，交互逻辑清晰

### 3. ⚡ 组件开发 (Component Development)
- 实现组件功能和样式
- 集成状态管理和 API 调用
- 优化性能和用户体验
- **转换条件**：功能完成，自测通过

### 4. 🧪 测试验证 (Testing)
- 单元测试和集成测试
- 跨浏览器兼容性测试
- 性能和可访问性测试
- **转换条件**：测试通过，质量达标

### 5. 📦 构建部署 (Build & Deploy)
- 代码打包和优化
- 部署到测试/生产环境
- 监控和性能分析
- **转换条件**：部署成功，运行稳定

## 🛠️ Technology Stack - 技术栈

### Core Frameworks - 核心框架
- **Vue 3** - Composition API, `<script setup>`
- **React 18+** - Hooks, Concurrent Features
- **TypeScript** - 类型安全和开发体验

### Build Tools - 构建工具
- **Vite** - 快速开发和构建
- **Webpack** - 复杂项目配置

### UI Libraries - UI库
- **Element Plus** (Vue)
- **Ant Design** (React)
- **Tailwind CSS** - 原子化CSS

### State Management - 状态管理
- **Pinia** (Vue)
- **Zustand** (React)
- **Redux Toolkit** (React)

## ✅ Best Practices - 最佳实践

### Component Development - 组件开发
- 使用 Composition API (Vue) 或 Hooks (React)
- 保持组件单一职责
- 合理使用 props 和 emit/callback
- 实现响应式设计

### Code Quality - 代码质量
- TypeScript 严格模式
- ESLint + Prettier 代码规范
- 组件和函数命名规范
- 充分的注释和文档

### Performance - 性能优化
- 懒加载和代码分割
- 图片优化和压缩
- 缓存策略
- Bundle 分析和优化

## 🔧 Development Commands - 开发命令

```bash
# 项目初始化
npm create vue@latest my-project  # Vue 3
npx create-react-app my-app --template typescript  # React

# 开发服务器
npm run dev

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint
npm run type-check
```

## 📋 Checklist - 检查清单

### 开发前
- [ ] 需求分析完成
- [ ] 技术栈选择确定
- [ ] 项目结构规划
- [ ] 开发环境配置

### 开发中
- [ ] 组件设计合理
- [ ] 代码规范遵循
- [ ] 类型定义完整
- [ ] 单元测试编写

### 开发后
- [ ] 功能测试通过
- [ ] 性能指标达标
- [ ] 代码审查完成
- [ ] 文档更新完整

## 🎯 Mode Switching - 模式切换

根据当前任务自动或手动切换到对应模式：

- **UI设计模式** - `/ui-design`
- **组件开发模式** - `/component`
- **测试模式** - `/test-frontend`
- **优化模式** - `/optimize`
- **部署模式** - `/deploy`

### 前端专用配置模式行为
- **Vue优先模式** - 激活后优先推荐 Vue 3 + Composition API 技术栈
- **React优先模式** - 激活后优先推荐 React 18+ + Hooks 技术栈
- **TypeScript严格模式** - 激活后启用最严格的类型检查和类型安全
- **性能优化模式** - 激活后自动建议代码分割、懒加载、缓存策略
