{"Favorite": "가장 좋아하는", "Tag": "태그", "Duplicate": "복제하기", "Persona": "페르소나", "Delete": "삭제", "AI Response Configuration": "AI 응답 구성", "AI Configuration panel will stay open": "AI 구성 패널이 열린 상태로 유지됩니다", "clickslidertips": "수동으로 값을 입력하려면 클릭하세요.", "MAD LAB MODE ON": "미치광이 연구실 모드 켜짐", "Documentation on sampling parameters": "샘플링 매개 변수에 대한 문서", "kobldpresets": "코볼드 사전 설정", "guikoboldaisettings": "KoboldAI 인터페이스 설정", "Update current preset": "현재 프리셋 업데이트", "Save preset as": "프리셋으로 저장", "Import preset": "프리셋 가져오기", "Export preset": "프리셋 내보내기", "Restore current preset": "현재 프리셋 복원", "Delete the preset": "프리셋 삭제", "novelaipresets": "NovelAI 사전 설정", "Default": "기본값", "openaipresets": "OpenAI 사전 설정", "Text Completion presets": "텍스트 완성 프리셋", "AI Module": "AI 모듈", "Changes the style of the generated text.": "생성된 텍스트의 스타일을 변경합니다.", "No Module": "모듈 없음", "Instruct": "지시", "Prose Augmenter": "산문 증강기", "Text Adventure": "텍스트 어드벤처", "response legth(tokens)": "응답 길이 (토큰)", "Streaming": "스트리밍", "Streaming_desc": "생성되는대로 응답을 조금씩 표시하세요", "context size(tokens)": "컨텍스트 크기 (토큰)", "unlocked": "잠금 해제됨", "Only enable this if your model supports context sizes greater than 8192 tokens": "모델이 8192 토큰보다 큰 컨텍스트 크기를 지원하는 경우에만 활성화하세요", "Max prompt cost:": "최대 프롬프트 비용:", "Display the response bit by bit as it is generated.": "생성되는 대답을 조금씩 표시합니다.", "When this is off, responses will be displayed all at once when they are complete.": "이 기능이 꺼져 있으면 대답은 완료되면 한 번에 모두 표시됩니다.", "Temperature": "온도", "rep.pen": "반복 페널티", "Rep. Pen. Range.": "반복 페널티 범위", "Rep. Pen. Slope": "대표적 페널티 기울기", "Rep. Pen. Freq.": "반복 페널티 빈도", "Rep. Pen. Presence": "반복 페널티 존재", "TFS": "TFS", "Phrase Repetition Penalty": "구절 반복 페널티", "Off": "끄기", "Very light": "매우 가벼운", "Light": "가벼운", "Medium": "중간", "Aggressive": "공격적", "Very aggressive": "매우 공격적", "Unlocked Context Size": "잠금 해제된 컨텍스트 크기", "Unrestricted maximum value for the context slider": "컨텍스트 슬라이더에 대한 제한 없는 최대값", "Context Size (tokens)": "컨텍스트 크기 (토큰)", "Max Response Length (tokens)": "최대 응답 길이 (토큰)", "Multiple swipes per generation": "세대당 다중 스와이프", "Enable OpenAI completion streaming": "OpenAI 완성 스트리밍 활성화", "Frequency Penalty": "빈도 페널티", "Presence Penalty": "존재 페널티", "Count Penalty": "카운트 페널티", "Top K": "상위 K", "Top P": "상위 P", "Repetition Penalty": "반복 페널티", "Min P": "최소 P", "Top A": "상위 A", "Quick Prompts Edit": "빠른 프롬프트 편집", "Main": "주요", "NSFW": "NSFW", "Jailbreak": "탈옥", "Utility Prompts": "유틸리티 프롬프트", "Impersonation prompt": "사칭 프롬프트", "Restore default prompt": "기본 프롬프트 복원", "Prompt that is used for Impersonation function": "사칭 기능에 사용되는 프롬프트", "World Info Format Template": "월드 인포 형식 템플릿", "Restore default format": "기본 형식 복원", "Wraps activated World Info entries before inserting into the prompt.": "프롬프트에 삽입하기 전에 활성화된 월드 인포 항목을 래핑합니다.", "scenario_format_template_part_1": "사용", "scenario_format_template_part_2": "내용이 삽입된 위치를 표시합니다.", "Scenario Format Template": "시나리오 형식 템플릿", "Personality Format Template": "성격 형식 템플릿", "Group Nudge Prompt Template": "그룹 너지 프롬프트 템플릿", "Sent at the end of the group chat history to force reply from a specific character.": "특정 캐릭터의 답장을 강제하기 위해 그룹 채팅 기록 마지막에 전송됩니다.", "New Chat": "새 채팅", "Restore new chat prompt": "새 채팅 메시지 복원", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "새 채팅이 곧 시작될 것임을 나타내기 위해 채팅 기록 시작 부분에 설정합니다.", "New Group Chat": "새 그룹 채팅", "Restore new group chat prompt": "기본 프롬프트 복원", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "새 그룹 채팅이 곧 시작될 것임을 나타내기 위해 채팅 기록 시작 부분에 설정합니다.", "New Example Chat": "새로운 예시 채팅", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "새로운 예시 채팅이 곧 시작될 것임을 나타내기 위해 대화 예시의 시작 부분에 설정합니다.", "Continue nudge": "계속 넛지", "Set at the end of the chat history when the continue button is pressed.": "계속 버튼을 누르면 채팅 기록이 끝날 때 설정됩니다.", "Replace empty message": "빈 메시지 대체", "Send this text instead of nothing when the text box is empty.": "텍스트 상자가 비어 있을 때 이 텍스트를 아무것도 없이 보내세요.", "Seed": "시드", "Set to get deterministic results. Use -1 for random seed.": "결정적인 결과를 얻으려면 설정하세요. 무작위 시드를 위해서는 -1 값을 사용합니다.", "Temperature controls the randomness in token selection": "온도는 토큰 선택에서의 무작위성을 제어합니다.", "Top_K_desc": "Top K는 선택할 수 있는 최대 상위 토큰 양을 설정합니다.", "Top_P_desc": "Top P (일명 핵심 샘플링)", "Typical P": "전형적인 P", "Typical_P_desc": "전형적인 P 샘플링은 집합의 평균 엔트로피와의 편차를 기반으로 토큰에 우선순위를 부여합니다.", "Min_P_desc": "Min P는 기본 최소 확률을 설정합니다.", "Top_A_desc": "Top A는 가장 높은 토큰 확률의 제곱에 기반하여 토큰 선택에 대한 임계값을 설정합니다.", "Tail_Free_Sampling_desc": "꼬리 제거 샘플링 (TFS)", "rep.pen range": "반복 페널티 범위", "Mirostat": "Mirostat", "Mode": "모드", "Mirostat_Mode_desc": "값이 0이면 Mirostat가 완전히 비활성화됩니다. 1은 Mirostat 1.0용이고 2는 Mirostat 2.0용입니다.", "Tau": "Tau", "Mirostat_Tau_desc": "Mirostat 출력의 가변성을 제어합니다.", "Eta": "Eta", "Mirostat_Eta_desc": "Mirostat의 학습률을 제어합니다.", "Ban EOS Token": "EOS 토큰 금지", "Ban_EOS_Token_desc": "KoboldCpp가 포함된 EOS(End-of-Sequence) 토큰(및 KoboldAI가 포함된 다른 토큰도 가능)을 금지합니다.\r스토리 작성에 적합하지만 채팅 및 교육 모드에는 사용하면 안 됩니다.", "GBNF Grammar": "GBNF 문법", "Type in the desired custom grammar": "원하는 사용자 정의 문법을 입력하세요.", "Samplers Order": "샘플러 순서", "Samplers will be applied in a top-down order. Use with caution.": "샘플러는 위에서 아래로 적용됩니다. 주의하여 사용하세요.", "Tail Free Sampling": "Tail Free 샘플링", "Load koboldcpp order": "kobold CPP 순서로 로드", "Preamble": "서두", "Use style tags to modify the writing style of the output.": "스타일 태그를 사용하여 출력의 쓰기 스타일을 수정하세요.", "Banned Tokens": "금지된 토큰", "Sequences you don't want to appear in the output. One per line.": "출력에 나타나지 않길 원하는 시퀀스입니다. 한 줄에 하나씩.", "Logit Bias": "Logit 편향", "Add": "추가", "Helps to ban or reenforce the usage of certain words": "특정 단어의 사용을 금지하거나 강화하는데 도움이 됩니다.", "CFG Scale": "CFG 스케일", "Negative Prompt": "부정적 프롬프트", "Add text here that would make the AI generate things you don't want in your outputs.": "AI가 출력에서 원하지 않는 것을 생성하도록하는 텍스트를 여기에 추가하세요.", "Used if CFG Scale is unset globally, per chat or character": "CFG 스케일이 전역적으로 설정되지 않은 경우, 각 채팅 또는 각 캐릭터마다 사용됩니다.", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "Mirostat LR", "Min Length": "최소 길이", "Top K Sampling": "상위 K 샘플링", "Nucleus Sampling": "Nucleus 샘플링", "Top A Sampling": "상위 A 샘플링", "CFG": "CFG", "Neutralize Samplers": "샘플러 중화", "Set all samplers to their neutral/disabled state.": "모든 샘플러를 중립/비활성 상태로 설정하세요.", "Sampler Select": "샘플러 선택", "Customize displayed samplers or add custom samplers.": "표시된 샘플러를 사용자 정의하거나 사용자 정의 샘플러를 추가하세요.", "Epsilon Cutoff": "Epsilon 자르기", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon 절단은 토큰이 샘플링에서 제외되는 확률 하한선을 설정합니다.", "Eta Cutoff": "Eta 자르기", "Eta_Cutoff_desc": "Eta 절단은 특별한 Eta 샘플링 기술의 주요 매개 변수입니다.&#13;1e-4 단위로; 합리적인 값은 3입니다.&#13;비활성화하려면 0으로 설정하세요.&#13;자세한 <PERSON> et al. (2022)의 Truncation Sampling as Language Model Desmoothing 논문을 참조하세요.", "rep.pen decay": "반복 페널티 붕괴", "Encoder Rep. Pen.": "인코더 반복 페널티", "No Repeat Ngram Size": "반복 없는 Ngram 크기", "Skew": "비스듬한", "Max Tokens Second": "초당 최대 토큰", "Smooth Sampling": "부드러운 샘플링", "Smooth_Sampling_desc": "2차/3차 변환을 사용하여 분포를 조정할 수 있습니다. 평활화 요소 값이 낮을수록 더 창의적이 됩니다. 일반적으로 0.2-0.3 사이가 가장 적합합니다(곡선 = 1로 가정). 평활화 곡선 값이 높을수록 곡선이 더 가파르게 변하고 확률이 낮은 선택을 더욱 적극적으로 처벌하게 됩니다. 1.0 곡선은 Smoothing Factor만 사용하는 것과 같습니다.", "Smoothing Factor": "평활화 요소", "Smoothing Curve": "평활화 곡선", "DRY_Repetition_Penalty_desc": "DRY는 입력의 끝을 이전에 입력에서 발생한 시퀀스로 확장하는 토큰에 페널티를 적용합니다. 비활성화하려면 승수를 0으로 설정하세요.", "DRY Repetition Penalty": "DRY 반복 페널티", "DRY_Multiplier_desc": "DRY를 활성화하려면 값 > 0으로 설정하세요. 가장 짧은 페널티를 받는 시퀀스에 대한 페널티의 크기를 제어합니다.", "Multiplier": "승수", "DRY_Base_desc": "시퀀스 길이가 증가함에 따라 페널티가 증가하는 속도를 제어합니다.", "Base": "베이스", "DRY_Allowed_Length_desc": "불이익을 받지 않고 반복할 수 있는 가장 긴 시퀀스입니다.", "Allowed Length": "허용되는 길이", "Penalty Range": "페널티 범위", "DRY_Sequence_Breakers_desc": "시퀀스 일치가 계속되지 않는 토큰입니다. 따옴표로 묶인 문자열의 쉼표로 구분된 목록으로 지정됩니다.", "Sequence Breakers": "시퀀스 차단기", "JSON-serialized array of strings.": "JSON으로 직렬화된 문자열 배열입니다.", "Dynamic Temperature": "동적 온도", "Scale Temperature dynamically per token, based on the variation of probabilities": "확률의 변동을 기반으로 토큰마다 온도를 동적으로 조정합니다.", "Minimum Temp": "최소 온도", "Maximum Temp": "최대 온도", "Exponent": "지수", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (mode=1은 llama.cpp 전용입니다)", "Mirostat_desc": "Mirostat은 언어 모델의 출력 텍스트의 복잡도(perplexity)를 직접 제어하여 더 자연스러운 텍스트를 생성하도록 하는 적응형 샘플링 알고리즘입니다", "Mirostat Mode": "Mirostat 모드", "Variability parameter for Mirostat outputs": "Mirostat 출력의 변동성 매개변수", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "Mirostat 학습률", "Beam search": "빔 검색", "Helpful tip coming soon.": "유용한 팁을 곧 알려드리겠습니다.", "Number of Beams": "빔의 수", "Length Penalty": "길이 페널티", "Early Stopping": "조기 중지", "Contrastive search": "대조적 검색", "Penalty Alpha": "페널티 알파", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "대조적 검색 정규화 항의 강도입니다. CS를 비활성화하려면 0으로 설정하세요.", "Do Sample": "샘플", "Add BOS Token": "BOS 토큰 추가", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "프롬프트의 시작 부분에 bos_token을 추가하세요. 이를 비활성화하면 응답이 더 창의적으로 될 수 있습니다.", "Ban the eos_token. This forces the model to never end the generation prematurely": "eos_token을 금지하세요. 이는 모델이 생성을 이르게 종료하지 않도록 합니다", "Ignore EOS Token": "EOS 토큰 무시", "Ignore the EOS Token even if it generates.": "EOS 토큰이 생성되더라도 무시하세요.", "Skip Special Tokens": "특수 토큰 건너뛰기", "Temperature Last": "마지막 온도", "Temperature_Last_desc": "마지막으로 온도 샘플러를 사용합니다.", "Speculative Ngram": "투기적 Ngram", "Use a different speculative decoding method without a draft model": "초안 모델 없이 다른 추측적 디코딩 방법을 사용합니다.\r초안 모델을 사용하는 것이 좋습니다. 투기적 ngram은 그다지 효과적이지 않습니다.", "Spaces Between Special Tokens": "특수 토큰 사이의 공백", "LLaMA / Mistral / Yi models only": "LLaMA / Mistral / Yi 모델 전용", "Example: some text [42, 69, 1337]": "예: 일부 텍스트 [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "분류기 무료 안내. 더 유용한 팁이 곧 제공됩니다.", "Scale": "스케일", "JSON Schema": "JSON 스키마", "Type in the desired JSON schema": "원하는 JSON 스키마를 입력하세요.", "Grammar String": "문법 문자열", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF 또는 EBNF는 사용 중인 백엔드에 따라 다릅니다. 이것을 사용한다면 어느 것이 무엇인지 알아야합니다.", "Top P & Min P": "상위 P 및 최소 P", "Load default order": "기본 순서로 로드", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "llama.cpp만 가능합니다. 샘플러의 순서를 결정합니다. Mirostat 모드가 0이 아닌 경우 샘플러 순서는 무시됩니다.", "Sampler Priority": "샘플러 우선 순위", "Ooba only. Determines the order of samplers.": "<PERSON><PERSON><PERSON> 전용. 샘플러의 순서를 결정합니다.", "Character Names Behavior": "캐릭터 이름 동작", "character_names_none": "캐릭터 이름 접두사를 추가하지 않습니다. 그룹 채팅에서는 좋지 않을 수 있으므로, 이 설정을 선택할 때는 주의해야 합니다.", "Helps the model to associate messages with characters.": "모델이 메시지를 캐릭터와 연관시키는 데 도움이 됩니다.", "None": "없음", "None (not injected)": "없음 (삽입되지 않음)", "character_names_default": "그룹과 과거 페르소나에 대해 접두사를 추가합니다. 그 외의 경우에는 프롬프트에 이름을 직접 제공해야 합니다.", "Don't add character names.": "캐릭터 이름을 추가하지 마세요.", "Completion": "완료 객체", "character_names_completion": "제한 사항이 적용됩니다. 라틴 알파벳, 숫자, 밑줄만 사용 가능합니다. 모든 소스, 특히 Claude, <PERSON><PERSON><PERSON>A<PERSON>, Google에서 작동하지 않습니다.", "Add character names to completion objects.": "완성 객체에 캐릭터 이름을 추가합니다.", "Message Content": "메시지 내용", "Prepend character names to message contents.": "메시지 내용 앞에 캐릭터의 이름을 추가합니다.", "Continue Postfix": "계속하기 접미사", "The next chunk of the continued message will be appended using this as a separator.": "계속되는 메시지의 다음 청크는 이를 구분 기호로 사용하여 추가됩니다.", "Space": "한 칸 띄우기", "Newline": "새로운 줄", "Double Newline": "이중 줄", "Wrap user messages in quotes before sending": "전송 전에 사용자 메시지를 따옴표로 둘러싸기", "Wrap in Quotes": "따옴표로 메시지 감싸기", "Wrap entire user message in quotes before sending.": "사용자 메시지 전체를 보내기 전에 따옴표로 감싸집니다.", "Leave off if you use quotes manually for speech.": "말하기 위해 수동으로 따옴표를 사용하는 경우 체크하지 마세요.", "Continue prefill": "사전 작성 계속", "Continue sends the last message as assistant role instead of system message with instruction.": "계속하면 지시와 함께 시스템 메시지 대신 마지막 메시지를 어시스턴트 역할로 보냅니다.", "Squash system messages": "시스템 메시지 압축", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "연속된 시스템 메시지를 하나로 결합합니다(예제 대화 제외). 일부 모델의 일관성을 향상시킬 수 있습니다.", "Enable function calling": "함수 호출 활성화", "Send inline images": "인라인 이미지 전송", "image_inlining_hint_1": "모델이 지원하는 경우 메시지로 이미지를 보냅니다.\n                                                사용", "image_inlining_hint_2": "메시지에 대한 조치 또는", "image_inlining_hint_3": "채팅에 이미지 파일을 첨부하는 메뉴입니다.", "Inline Image Quality": "인라인 이미지 품질", "openai_inline_image_quality_auto": "자동", "openai_inline_image_quality_low": "낮은", "openai_inline_image_quality_high": "높은", "Use AI21 Tokenizer": "AI21 토크나이저 사용", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "GPT보다 효율적인 Jurassic 모델에 적합한 토크나이저를 사용하세요.", "Use Google Tokenizer": "구글 토크나이저 사용", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Google 모델용 적절한 토크나이저를 사용하여 API를 통해 제공됩니다. 더 느린 프롬프트 처리지만 훨씬 정확한 토큰 계산을 제공합니다.", "Use system prompt": "시스템 프롬프트 사용", "(Gemini 1.5 Pro/Flash only)": "(Gemini 1.5 Pro/Flash 전용)", "Merges_all_system_messages_desc_1": "비시스템 역할이 있는 첫 번째 메시지까지 모든 시스템 메시지를 병합하여", "Merges_all_system_messages_desc_2": "필드.", "Assistant Prefill": "어시스턴트 프리필", "Start Claude's answer with...": "클로드의 답변 시작하기...", "Assistant Impersonation Prefill": "어시스턴트 사칭 프리필", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "지원되는 모델에 대한 시스템 프롬프트를 보냅니다. 비활성화된 경우 사용자 메시지가 프롬프트의 처음에 추가됩니다.", "User first message": "사용자 첫 번째 메시지", "Restore User first message": "사용자의 첫 번째 메시지 복원", "Human message": "인간 메시지, 지시 등\n비어 있으면 아무것도 추가하지 않습니다. 즉, '사용자' 역할이 있는 새 프롬프트가 필요합니다.", "New preset": "새 프리셋", "Delete preset": "프리셋 삭제", "View / Edit bias preset": "바이어스 프리셋 보기/편집", "Add bias entry": "바이어스 항목 추가", "Most tokens have a leading space.": "대부분의 토큰에는 선행 공백이 있습니다.", "API Connections": "API 연결", "Text Completion": "Text Completion", "Chat Completion": "Chat Completion", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "민감한 정보를 Horde에 보내지 않도록 합니다.", "Review the Privacy statement": "개인 정보 보호 정책 검토", "Register a Horde account for faster queue times": "대기 시간 감축을 원한다면 Horde 계정을 등록하세요.", "Learn how to contribute your idle GPU cycles to the Horde": "여유로운 GPU 주기를 Horde에 기여하는 방법 배우기", "Adjust context size to worker capabilities": "컨텍스트 크기를 작업자 기능에 맞게 조정합니다.", "Adjust response length to worker capabilities": "응답 길이를 작업자 기능에 맞게 조정합니다.", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "승인된 작업자만을 대기열에 넣어 나쁜 응답을 처리할 수 있습니다. 응답 시간이 느려질 수 있습니다.", "Trusted workers only": "신뢰할 수 있는 작업자만 허용", "API key": "API 키", "Get it here:": "여기에서 얻으세요:", "Register": "등록", "View my Kudos": "내 Kudos 보기", "Enter": "입력", "to use anonymous mode.": "익명 모드를 사용하려면.", "Clear your API key": "API 키 지우기", "For privacy reasons, your API key will be hidden after you reload the page.": "개인 정보 보호를 위해 페이지를 다시로드한 후에는 API 키가 숨겨집니다.", "Models": "모델", "Refresh models": "모델 새로 고침", "-- Horde models not loaded --": "-- Horde 모델 로드되지 않음 --", "Not connected...": "연결되지 않음...", "API url": "API URL", "Example: http://127.0.0.1:5000/api ": "예시: http://127.0.0.1:5000/api", "Connect": "연결", "Cancel": "취소", "Novel API key": "NovelAPI 키", "Get your NovelAI API Key": "NovelAI API 키 가져오기", "Enter it in the box below": "아래 칸에 복사한 API 키를 입력하세요.", "Novel AI Model": "Novel AI 모델", "No connection...": "연결 되지 않음...", "API Type": "API 유형", "Default (completions compatible)": "기본값 [OpenAI /완성 호환: oobabooga, LM Studio 등]", "TogetherAI API Key": "TogetherAI API 키", "TogetherAI Model": "TogetherAI 모델", "-- Connect to the API --": "-- API에 연결 --", "OpenRouter API Key": "OpenRouter API 키", "Click Authorize below or get the key from": "아래의 인증을 클릭하거나 다음에서 키를 받으세요", "View Remaining Credits": "남은 크레딧 보기", "OpenRouter Model": "OpenRouter 모델", "Model Providers": "모델 제공자", "InfermaticAI API Key": "InfermaticAI API 키", "InfermaticAI Model": "InfermaticAI 모델", "DreamGen API key": "DreamGen API 키", "DreamGen Model": "DreamGen 모델", "Mancer API key": "Mancer API 키", "Mancer Model": "<PERSON><PERSON> 모델", "Make sure you run it with": "실행하는지 확인하세요", "flag": "깃발", "API key (optional)": "API 키 (선택 사항)", "Server url": "서버 URL", "Example: http://127.0.0.1:5000": "예시: http://127.0.0.1:5000", "Custom model (optional)": "사용자 정의 모델 (선택 사항)", "vllm-project/vllm": "vllm-project/vllm(OpenAI API 래퍼 모드)", "vLLM API key": "vLLM API 키", "Example: http://127.0.0.1:8000": "예: http://127.0.0.1:8000", "vLLM Model": "vLLM 모델", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (OpenAI API의 래퍼 모드)", "Aphrodite API key": "Aphrodite API 키", "Aphrodite Model": "Aphrodite 모델", "ggerganov/llama.cpp": "ggerganov/llama.cpp (출력 서버)", "Example: http://127.0.0.1:8080": "예: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "예: http://127.0.0.1:11434", "Ollama Model": "Ollama 모델", "Download": "다운로드", "Tabby API key": "Tabby API 키", "koboldcpp API key (optional)": "koboldcpp API 키(선택사항)", "Example: http://127.0.0.1:5001": "예: http://127.0.0.1:5001", "Authorize": "승인하기", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "OAuth 플로우를 사용하여 OpenRouter API 토큰을 가져옵니다. openrouter.ai로 리디렉션됩니다.", "Legacy API (pre-OAI, no streaming)": "레거시 API (OAI 이전, 스트리밍 없음)", "Bypass status check": "상태 확인 우회", "Chat Completion Source": "채팅 완성 근원", "Reverse Proxy": "역방향 프록시", "Proxy Presets": "프록시 사전 설정", "Saved addresses and passwords.": "저장된 주소와 비밀번호.", "Save Proxy": "프록시 저장", "Delete Proxy": "프록시 삭제", "Proxy Name": "프록시 이름", "Allow reverse proxy": "역방향 프록시 허용", "This will show up as your saved preset.": "이는 저장된 사전 설정으로 표시됩니다.", "Proxy Server URL": "프록시 서버 URL", "Alternative server URL (leave empty to use the default value).": "대체 서버 URL (기본값 사용을 원할 경우 비워 둡니다).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "이 입력란에 어떤 것이든 입력하기 전에 반드시 실제 OAI API 키를 API 패널에서 제거하세요.", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "개발진은 비공식 OpenAI 프록시를 사용하는 동안 발생하는 문제에 대한 지원을 제공할 수 없습니다.", "Doesn't work? Try adding": "작동하지 않나요? 추가해 보세요", "at the end!": "마지막에!", "Proxy Password": "프록시 비밀번호", "Will be used as a password for the proxy instead of API key.": "API 키 대신 프록시의 비밀번호로 사용됩니다.", "Peek a password": "비밀번호를 엿보세요", "OpenAI API key": "OpenAI API 키", "View API Usage Metrics": "API 사용 지표 보기", "Follow": "OpenAI API 키를 얻으려면", "these directions": "이 지시 사항", "to get your OpenAI API key.": "을 따르세요.", "Use Proxy password field instead. This input will be ignored.": "대신 \"프록시 비밀번호\" 필드를 사용하세요. 이 입력은 무시됩니다.", "OpenAI Model": "OpenAI 모델", "Bypass API status check": "API 상태 확인 우회", "Show External models (provided by API)": "외부 모델 표시 (API 제공)", "Get your key from": "다음에서 키를 받으세요", "Anthropic's developer console": "Anthropic의 개발자 콘솔", "Slack and Poe cookies will not work here, do not bother trying.": "Slack과 Poe 쿠키는 여기서 작동하지 않습니다. 시도하지 마세요.", "Claude Model": "<PERSON> 모델", "Window AI Model": "Window AI 모델", "Model Order": "OpenRouter 모델 정렬", "Alphabetically": "알파벳순", "Price": "가격(가장 저렴)", "Context Size": "컨텍스트 크기", "Group by vendors": "공급업체별 그룹화", "Group by vendors Description": "OpenAI 모델을 한 그룹에 넣고, Anthropic 모델을 다른 그룹에 두는 등 정렬을 통해 결합할 수 있습니다.", "Allow fallback routes": "fallback 허용", "Allow fallback routes Description": "선택한 모델이 요청을 처리할 수 없는 경우 대체 모델이 자동으로 선택됩니다.", "openrouter_force_instruct": "이 옵션은 오래되었으며 향후 제거될 예정입니다. 지시 형식을 사용하려면 대신 Text Completion API에서 OpenRouter로 전환하세요.", "LEGACY": "유산", "Force Instruct Mode formatting": "강제 지시 모드 형식", "Force_Instruct_Mode_formatting_Description": "Instruct Mode와 이 모드가 모두 활성화된 경우 프롬프트는 SillyTavern에 의해 현재 형식을 사용하여 형식화됩니다.\n                                        고급 형식 설정(시스템 프롬프트 지시 제외) 비활성화된 경우 프롬프트는 OpenRouter에 의해 형식화됩니다.", "AI21 API Key": "AI21 API 키", "AI21 Model": "AI21 모델", "Google AI Studio API Key": "Google AI Studio API 키", "Google Model": "구글 모델", "MistralAI API Key": "MistralAI API 키", "MistralAI Model": "MistralAI 모델", "Groq API Key": "Groq API 키", "Groq Model": "Groq 모델", "Perplexity API Key": "Perplexity API 키", "Perplexity Model": "Perplexity 모델", "Cohere API Key": "Cohere API 키", "Cohere Model": "Cohere 모델", "Custom Endpoint (Base URL)": "사용자 정의 엔드포인트(기본 URL)", "Custom API Key": "커스텀 API 키", "Available Models": "사용 가능한 모델", "Prompt Post-Processing": "신속한 후처리", "api_no_connection": "연결이 되지 않았습니다...", "Applies additional processing to the prompt before sending it to the API.": "API로 보내기 전에 프롬프트에 추가 처리를 적용합니다.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "짧은 테스트 메시지를 보내어 API 연결을 확인합니다. 이에 대해 유료 크레딧이 지불될 수 있음을 인식하세요!", "Test Message": "테스트 메시지", "Auto-connect to Last Server": "마지막 서버에 자동으로 연결", "Missing key": "❌ 현재 저장된 키가 존재하지 않습니다.", "Key saved": "✔️ 키 저장됨.", "View hidden API keys": "숨겨진 API 키 보기", "AI Response Formatting": "AI 응답 형식 지정", "Advanced Formatting": "고급 서식", "Context Template": "컨텍스트 템플릿", "Auto-select this preset for Instruct Mode": "지시 모드에 대해 이 프리셋 자동 선택", "Story String": "이야기 문자열", "Example Separator": "예시 대화 구분자", "Chat Start": "채팅 시작", "Add Chat Start and Example Separator to a list of stopping strings.": "중지 문자열 목록에 채팅 시작 및 예제 구분 기호를 추가합니다.", "Use as Stop Strings": "중지 문자열로 사용", "Allow Jailbreak": "탈옥 허용", "Context Order": "컨텍스트 순서", "Summary": "요약", "Author's Note": "작가 노트", "Example Dialogues": "예시 대화", "Hint": "힌트:", "In-Chat Position not affected": "요약 및 작가 노트 삽입 순서는 In-Chat 위치가 설정되지 않은 경우에만 영향을 받습니다.", "Instruct Mode": "지시 모드", "Enabled": "활성화됨", "instruct_bind_to_context": "활성화되면 선택한 지시 템플릿 이름이나 기본 설정에 따라 컨텍스트 템플릿이 자동으로 선택됩니다.", "Bind to Context": "컨텍스트에 바인딩", "Presets": "프리셋", "Auto-select this preset on API connection": "API 연결 시 이 프리셋 자동 선택", "Activation Regex": "활성화 정규식", "Wrap Sequences with Newline": "새 줄로 시퀀스 감싸기", "Replace Macro in Sequences": "시퀀스에서 매크로 대체", "Skip Example Dialogues Formatting": "예제 대화 형식 건너뛰기", "Include Names": "이름 포함", "Force for Groups and Personas": "그룹 및 페르소나에 대한 강제 적용", "System Prompt": "시스템 프롬프트", "Instruct Mode Sequences": "지시 모드 시퀀스", "System Prompt Wrapping": "시스템 프롬프트 래핑", "Inserted before a System prompt.": "시스템 프롬프트 앞에 삽입됩니다.", "System Prompt Prefix": "시스템 프롬프트 접두사", "Inserted after a System prompt.": "시스템 프롬프트 뒤에 삽입됩니다.", "System Prompt Suffix": "시스템 프롬프트 접미사", "Chat Messages Wrapping": "채팅 메시지 래핑", "Inserted before a User message and as a last prompt line when impersonating.": "사용자 메시지 앞에 삽입되고 가장할 때 마지막 프롬프트 줄로 삽입됩니다.", "User Message Prefix": "사용자 메시지 접두사", "Inserted after a User message.": "사용자 메시지 뒤에 삽입됩니다.", "User Message Suffix": "사용자 메시지 접미사", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "어시스턴트 메시지 앞에 삽입되고 AI 답장을 생성할 때 마지막 프롬프트 라인으로 삽입됩니다.", "Assistant Message Prefix": "어시스턴트 메시지 접두사", "Inserted after an Assistant message.": "어시스턴트 메시지 뒤에 삽입됩니다.", "Assistant Message Suffix": "어시스턴트 메시지 접미사", "Inserted before a System (added by slash commands or extensions) message.": "시스템(슬래시 명령 또는 확장으로 추가) 메시지 앞에 삽입됩니다.", "System Message Prefix": "시스템 메시지 접두사", "Inserted after a System message.": "시스템 메시지 뒤에 삽입됩니다.", "System Message Suffix": "시스템 메시지 접미사", "If enabled, System Sequences will be the same as User Sequences.": "이 기능을 활성화하면, 시스템 시퀀스가 ​​사용자 시퀀스와 동일해집니다.", "System same as User": "사용자와 동일한 시스템", "Misc. Sequences": "기타 시퀀스", "Inserted before the first Assistant's message.": "어시스턴트의 첫 번째 메시지 앞에 삽입됩니다.", "First Assistant Prefix": "첫 번째 어시스턴트 접두어", "instruct_last_output_sequence": "마지막 어시스턴트 메시지 앞에 삽입되거나 AI 응답을 생성할 때 마지막 프롬프트 줄로 삽입됩니다(중립/시스템 역할 제외).", "Last Assistant Prefix": "마지막 어시스턴트 접두사", "Will be inserted as a last prompt line when using system/neutral generation.": "시스템/중립 생성을 사용할 때 마지막 프롬프트 라인으로 삽입됩니다.", "System Instruction Prefix": "시스템 명령어 접두사", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "정지 시퀀스가 ​​생성되면 그 이후의 모든 항목이 출력에서 ​​제거됩니다(포함).", "Stop Sequence": "중지 시퀀스", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "사용자 메시지로 시작하지 않는 경우 채팅 기록 시작 부분에 삽입됩니다.", "User Filler Message": "사용자 필러 메시지", "Context Formatting": "컨텍스트 서식", "(Saved to Context Template)": "(컨텍스트 템플릿에 저장됨)", "Always add character's name to prompt": "항상 캐릭터의 이름을 프롬프트에 추가", "Generate only one line per request": "요청당 한 줄만 생성", "Trim Incomplete Sentences": "불완전한 문장 자르기", "Include Newline": "새 줄 포함", "Misc. Settings": "기타 설정", "Collapse Consecutive Newlines": "연속적인 새 줄 축소", "Trim spaces": "공백 자르기", "Tokenizer": "토크나이저", "Token Padding": "토큰 패딩", "Start Reply With": "응답 시작", "AI reply prefix": "AI 답변 접두사", "Show reply prefix in chat": "채팅에서 응답 접두사 표시", "Non-markdown strings": "마크다운이 아닌 문자열", "separate with commas w/o space between": "쉼표로 구분 (공백 없이)", "Custom Stopping Strings": "사용자 정의 중지 문자열", "JSON serialized array of strings": "문자열의 JSON 직렬화된 배열", "Replace Macro in Stop Strings": "사용자 정의 중단 문자열에서 매크로 교체", "Auto-Continue": "자동 계속하기", "Allow for Chat Completion APIs": "채팅 완성 API 허용", "Target length (tokens)": "대상 길이 (토큰)", "World Info": "월드 인포", "Locked = World Editor will stay open": "잠금 = 월드 편집기가 열린 상태로 유지됩니다", "Worlds/Lorebooks": "월드 인포/로어북", "Active World(s) for all chats": "모든 채팅에 대한 활성화된 월드 인포(들)", "-- World Info not found --": "-- 월드 인포를 찾을 수 없음 --", "Global World Info/Lorebook activation settings": "글로벌 월드 인포/로어북 활성화 설정", "Click to expand": "펼치려면 클릭하세요.", "Scan Depth": "스캔 깊이", "Context %": "컨텍스트 %", "Budget Cap": "예산 한도", "(0 = disabled)": "(0 = 비활성화됨)", "Scan chronologically until reached min entries or token budget.": "최소 항목 또는 토큰 예산에 도달할 때까지 시간순으로 검색합니다.", "Min Activations": "최소 활성화", "Max Depth": "최대 깊이", "(0 = unlimited, use budget)": "(0 = 무제한, 예산 사용)", "Insertion Strategy": "삽입 전략", "Sorted Evenly": "균일하게 정렬됨", "Character Lore First": "캐릭터 로어 우선", "Global Lore First": "글로벌 로어 우선", "Entries can activate other entries by mentioning their keywords": "항목은 키워드를 언급하여 다른 항목을 활성화할 수 있습니다", "Recursive Scan": "재귀 스캔", "Lookup for the entry keys in the context will respect the case": "컨텍스트에서 항목 키를 검색할 때 대소문자를 지켜줍니다", "Case Sensitive": "대소문자 구분", "If the entry key consists of only one word, it would not be matched as part of other words": "항목 키가 하나의 단어로만 구성된 경우 다른 단어의 일부로 일치하지 않습니다", "Match Whole Words": "전체 단어 일치", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "포함 그룹 필터링을 위해 키 일치 수가 가장 많은 항목만 선택됩니다.", "Use Group Scoring": "그룹 점수 사용", "Alert if your world info is greater than the allocated budget.": "귀하의 월드 인포가 할당된 예산보다 큰 경우 경고합니다.", "Alert On Overflow": "오버플로우 알림", "New": "새 월드인포", "or": "또는", "--- Pick to Edit ---": "--- 편집할 항목 선택 ---", "Rename World Info": "월드 인포 이름 바꾸기", "Open all Entries": "모든 항목 열기", "Close all Entries": "모든 항목 닫기", "New Entry": "새 항목", "Fill empty Memo/Titles with Keywords": "비어 있는 메모/제목을 키워드로 채우기", "Import World Info": "월드 인포 가져오기", "Export World Info": "월드 인포 내보내기", "Duplicate World Info": "월드 인포 복제", "Delete World Info": "월드 인포 삭제", "Search...": "검색...", "Search": "찾기", "Priority": "우선 순위", "Custom": "사용자 정의", "Title A-Z": "제목 A-Z", "Title Z-A": "제목 Z-A", "Tokens ↗": "토큰 ↗", "Tokens ↘": "토큰 ↘", "Depth ↗": "깊이 ↗", "Depth ↘": "깊이 ↘", "Order ↗": "순서 ↗", "Order ↘": "순서 ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "발동 확률% ↗", "Trigger% ↘": "발동 확률% ↘", "Refresh": "새로 고침", "User Settings": "사용자 설정", "Simple": "간단한", "Advanced": "고급", "UI Language": "UI 언어", "Account": "계정", "Admin Panel": "관리자 패널", "Logout": "로그 아웃", "Search Settings": "검색 설정", "UI Theme": "UI 테마", "Import a theme file": "테마 파일 가져오기", "Export a theme file": "테마 파일 내보내기", "Delete a theme": "테마 삭제", "Update a theme file": "테마 파일 업데이트", "Save as a new theme": "새 테마로 저장", "Avatar Style:": "캐릭터 프로필 스타일", "Circle": "원", "Square": "정사각형", "Rectangle": "사각형", "Chat Style:": "채팅 스타일:", "Flat": "플랫\n버블\n문서", "Bubbles": "말풍선", "Document": "문서", "Specify colors for your theme.": "테마의 색상을 지정하세요.", "Theme Colors": "테마 색상", "Main Text": "주요 텍스트", "Italics Text": "이탤릭체 텍스트", "Underlined Text": "밑줄 텍스트", "Quote Text": "인용 텍스트", "Shadow Color": "그림자 색상", "Chat Background": "채팅 배경", "UI Background": "UI 배경", "UI Border": "UI 테두리", "User Message Blur Tint": "사용자 메시지 흐림 틴트", "AI Message Blur Tint": "AI 메시지 흐림 틴트", "Chat Width": "채팅창 폭", "Width of the main chat window in % of screen width": "화면 너비의 %로 표시되는 기본 채팅창 너비", "Font Scale": "글꼴 크기", "Font size": "글꼴 크기", "Blur Strength": "흐림 강도", "Blur strength on UI panels.": "UI 패널의 흐림 강도.", "Text Shadow Width": "텍스트 그림자 너비", "Strength of the text shadows": "텍스트 그림자의 강도", "Disables animations and transitions": "애니메이션 및 전환 비활성화", "Reduced Motion": "움직임 줄임", "removes blur from window backgrounds": "창 배경의 흐림 제거", "No Blur Effect": "흐림 효과 없음", "Remove text shadow effect": "텍스트 그림자 효과 제거", "No Text Shadows": "텍스트 그림자 없음", "Reduce chat height, and put a static sprite behind the chat window": "채팅 높이를 줄이고, 채팅 창 뒤에 정적 스프라이트를 넣습니다", "Waifu Mode": "와이프 모드", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "채팅 메시지의 메시지 액션 컨텍스트 항목의 전체 목록을 항상 표시하세요. 대신 '...' 뒤에 숨기지 않습니다.", "Auto-Expand Message Actions": "메시지 액션 자동 확장", "Alternative UI for numeric sampling parameters with fewer steps": "단계가 적은 숫자 샘플링 매개변수에 대한 대체 UI", "Zen Sliders": "젠 슬라이더", "Entirely unrestrict all numeric sampling parameters": "모든 숫자 샘플링 매개변수를 완전히 제한하지 않음", "Mad Lab Mode": "미치광이 연구실 모드", "Time the AI's message generation, and show the duration in the chat log": "AI의 메시지 생성 시간을 측정하고, 채팅 로그에 지속 시간을 표시합니다", "Message Timer": "메시지 도착 시간 측정", "Show a timestamp for each message in the chat log": "채팅 로그의 각 메시지에 타임스탬프 표시", "Chat Timestamps": "채팅 타임스탬프", "Show an icon for the API that generated the message": "메시지를 생성한 API에 대한 아이콘 표시", "Model Icon": "모델 아이콘 표시", "Show sequential message numbers in the chat log": "채팅 로그에 순차적인 메시지 번호 표시", "Message IDs": "메시지 ID", "Hide avatars in chat messages.": "채팅 메시지에서 아바타 숨김.", "Hide Chat Avatars": "캐릭터 프로필 숨기기", "Show the number of tokens in each message in the chat log": "채팅 로그의 각 메시지에 토큰 수 표시", "Show Message Token Count": "메시지 토큰 개수 표시", "Single-row message input area. Mobile only, no effect on PC": "한 줄짜리 메시지 입력 영역. 모바일 전용, PC에는 영향 없음", "Compact Input Area (Mobile)": "조그마한 입력 영역 (모바일)", "Swipe # for All Messages": "모든 스와이프 메시지에 대해 번호 매기기", "Display swipe numbers for all messages, not just the last.": "마지막 메시지만이 아니라 모든 메시지에 대한 스와이프 번호를 표시합니다.", "In the Character Management panel, show quick selection buttons for favorited characters": "캐릭터 관리 패널에서 즐겨찾는 캐릭터에 대한 빠른 선택 버튼을 표시합니다", "Characters Hotswap": "캐릭터 핫스왑", "Enable magnification for zoomed avatar display.": "마우스 포인터를 아바타 위에 올려두면 아바타가 확대 됩니다.", "Avatar Hover Magnification": "마우스를 올리면 아바타 확대", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "채팅에서 아바타 이미지를 클릭한 후 확대된 아바타를 표시할 때 마우스 오버 시 확대 효과를 활성화합니다.", "Show tagged character folders in the character list": "캐릭터 목록에서 태그가 지정된 캐릭터 폴더 표시", "Tags as Folders": "태그를 폴더로 설정", "Tags_as_Folders_desc": "최근 변경 사항: 태그는 태그 관리 메뉴에서 폴더로 표시되어야 폴더로 표시됩니다. 여기를 클릭해 불러오세요.", "Background Image": "채팅 배경 이미지", "Character Handling": "캐릭터 처리", "If set in the advanced character definitions, this field will be displayed in the characters list.": "고급 캐릭터 정의에서 설정된 경우 이 필드가 캐릭터 목록에 표시됩니다.", "Char List Subheader": "캐릭터 목록 하위 제목", "Character Version": "캐릭터 버전", "Created by": "제작자", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "퍼지 매칭을 사용하고 이름 하위 문자열뿐만 아니라 모든 데이터 필드로 목록에서 캐릭터를 검색합니다", "Advanced Character Search": "고급 캐릭터 검색", "If checked and the character card contains a prompt override (System Prompt), use that instead": "선택되어 있고 캐릭터 카드에 프롬프트(시스템 프롬프트) 재정의가 포함 된 경우, 그것을 대신 사용합니다", "Prefer Character Card Prompt": "캐릭터 카드 프롬프트 선호", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "선택되어 있고 캐릭터 카드에 (Post-History 지시)탈옥 재정의가 포함 된 경우, 그것을 대신 사용합니다.", "Prefer Character Card Jailbreak": "캐릭터 카드 탈옥 선호", "never_resize_avatars_tooltip": "가져온 캐릭터 이미지를 자르거나 크기를 조정하지 마세요. 꺼져 있으면 512x768로 자르거나 크기를 조정합니다.", "Never resize avatars": "아바타 크기 변경하지 않음", "Show actual file names on the disk, in the characters list display only": "실제 파일 이름을 디스크에 표시하며 캐릭터 목록 디스플레이에만", "Show avatar filenames": "아바타 파일 이름 표시", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "캐릭터 가져오기시 내장된 카드 태그를 가져 오도록 요청하세요. 그렇지 않으면 내장된 태그가 무시됩니다", "Import Card Tags": "카드 태그 가져오기", "Hide character definitions from the editor panel behind a spoiler button": "스포일러 버튼 뒤에 편집기 패널에서 캐릭터 정의를 숨깁니다", "Spoiler Free Mode": "스포일러 무료 모드", "Miscellaneous": "기타", "Reload and redraw the currently open chat": "현재 열려 있는 채팅을 다시로드하고 다시 그립니다", "Reload Chat": "채팅 다시 로드", "Debug Menu": "디버그 메뉴", "Smooth Streaming": "부드러운 스트리밍", "Experimental feature. May not work for all backends.": "실험적인 기능으로, 모든 백엔드에서 작동이 보장되지는 않을 수 있습니다.", "Slow": "느린", "Fast": "빠른", "Play a sound when a message generation finishes": "메시지 생성이 완료될 때 소리 재생", "Message Sound": "메시지 소리", "Only play a sound when ST's browser tab is unfocused": "ST의 브라우저 탭이 초점을 잃으면 소리를 재생합니다.", "Background Sound Only": "배경 소리만", "Reduce the formatting requirements on API URLs": "API URL의 서식 요구 사항 감소", "Relaxed API URLS": "완화된 API URLS", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "내장된 로어북이있는 모든 새 캐릭터에 대해 월드 인포/로어북을 가져 오도록 요청하세요. 선택 해제하면 대신 간단한 메시지가 표시됩니다", "Lorebook Import Dialog": "로어북 가져오기 대화 상자", "Restore unsaved user input on page refresh": "페이지 새로 고침시 저장되지 않은 사용자 입력 복원", "Restore User Input": "사용자 입력 복원", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "드래그하여 특정 UI 요소의 위치를 조정할 수 있습니다. PC 전용, 모바일에는 영향을주지 않습니다", "Movable UI Panels": "이동 가능한 UI 패널", "MovingUI preset. Predefined/saved draggable positions": "MovingUI 사전 설정. 미리 정의된/저장된 드래그 가능한 위치", "MUI Preset": "MUI 프리셋", "Save movingUI changes to a new file": "새 파일로 movingUI 변경 사항 저장", "Reset MovingUI panel sizes/locations.": "MovingUI 패널 크기/위치를 재설정합니다.", "Apply a custom CSS style to all of the ST GUI": "ST GUI의 모든 곳에 사용자 정의 CSS 스타일 적용", "Custom CSS": "사용자 정의 CSS", "Expand the editor": "편집기 확장", "Chat/Message Handling": "채팅/메시지 처리", "# Messages to Load": "로딩할 메시지 수", "The number of chat history messages to load before pagination.": "페이지를 매기기 전에 로드할 채팅 기록 메시지 수입니다.", "(0 = All)": "(0 = 모두)", "Streaming FPS": "스트리밍 FPS", "Update speed of streamed text.": "스트리밍된 텍스트의 속도를 업데이트합니다.", "Example Messages Behavior": "예제 메시지 동작", "Gradual push-out": "토큰 초과 시 점진적 밀어내기", "Always include examples": "항상 컨텍스트에 예제 포함", "Never include examples": "절대로 예제 포함 안 함", "Send on Enter": "Enter로 전송", "Disabled": "비활성화됨", "Automatic (PC)": "자동 (PC)", "Press Send to continue": "계속하려면 보내기를 누르세요", "Show a button in the input area to ask the AI to continue (extend) its last message": "입력 영역에 AI에게 마지막 메시지를 계속 (확장)하도록 요청하는 버튼을 표시합니다", "Quick 'Continue' button": "빠른 '계속' 버튼", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "대체 AI 응답을 생성하는 마지막 채팅 메시지에 화살표 버튼을 표시합니다. PC 및 모바일 모두", "Swipes": "스와이프", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "마지막 채팅 메시지에서 스와이프 생성을 트리거하기 위해 스와이핑 제스처 사용을 허용합니다. 모바일 전용, PC에는 영향이 없습니다", "Gestures": "제스처", "Auto-load Last Chat": "마지막 채팅 자동로드", "Auto-scroll Chat": "채팅 자동 스크롤", "Save edits to messages without confirmation as you type": "입력하는 동안 확인없이 메시지에 대한 편집 사항 저장", "Auto-save Message Edits": "메시지 편집 자동 저장", "Confirm message deletion": "메시지 삭제 확인", "Auto-fix Markdown": "마크다운 자동 수정", "Render LaTeX and AsciiMath equation notation in chat messages. Powered by KaTeX": "채팅 메시지에서 LaTeX 및 AsciiMath 방정식 표기법을 렌더링합니다. KaTeX 제공", "Render Formulas": "수식 렌더링", "Disallow embedded media from other domains in chat messages": "채팅 메시지에 다른 도메인의 삽입된 미디어를 허용하지 않습니다.", "Forbid External Media": "외부 미디어 금지", "Allow {{char}}: in bot messages": "봇 메시지에서 {{char}}: 허용", "Allow {{user}}: in bot messages": "봇 메시지에서 {{user}}: 허용", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "메시지 텍스트에서 인코딩 및 문자를 건너 뛰어 일부 HTML 마크업 및 Markdown을 허용합니다", "Show tags in responses": "응답에서 태그 표시", "Allow AI messages in groups to contain lines spoken by other group members": "그룹의 AI 메시지가 다른 그룹 멤버가 말한 줄을 포함 할 수 있도록 허용", "Relax message trim in Groups": "그룹에서 메시지 트리밍 완화", "Log prompts to console": "콘솔에 프롬프트 기록", "Requests logprobs from the API for the Token Probabilities feature": "토큰 확률 기능에 대한 API에서 로그 확률 요청", "Request token probabilities": "토큰 확률 요청", "Automatically reject and re-generate AI message based on configurable criteria": "구성 가능한 기준에 따라 AI 메시지를 자동으로 거부하고 다시 생성", "Auto-swipe": "자동 스와이프", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "자동 스와이프 기능을 활성화합니다. 이 섹션의 설정은 자동 스와이프가 활성화되었을 때만 영향을 미칩니다", "Minimum generated message length": "생성된 메시지 최소 길이", "If the generated message is shorter than these many characters, trigger an auto-swipe": "생성된 메시지가이보다 짧으면 자동 스와이프를 트리거합니다", "Blacklisted words": "금지어", "words you dont want generated separated by comma ','": "쉼표로 구분된 생성하지 않으려는 단어", "Blacklisted word count to swipe": "스와이프할 금지어 개수", "Minimum number of blacklisted words detected to trigger an auto-swipe": "자동 스와이프를 트리거하는 검출된 금지어의 최소 개수", "AutoComplete Settings": "자동 완성 설정", "Automatically hide details": "세부정보 자동 숨기기", "Determines how entries are found for autocomplete.": "자동 완성을 위해 항목을 찾는 방법을 결정합니다.", "Autocomplete Matching": "자동 완성 매칭", "Starts with": "시작하는 단어로", "Includes": "포함하는", "Fuzzy": "퍼지 매칭", "Sets the style of the autocomplete.": "자동완성 스타일을 설정합니다.", "Autocomplete Style": "자동 완성 스타일", "Follow Theme": "테마 적용", "Dark": "다크 모드", "Sets the font size of the autocomplete.": "자동 완성 글꼴 크기 설정", "Sets the width of the autocomplete.": "자동 완성의 너비를 설정합니다.", "Autocomplete Width": "자동 완성 너비 조절", "chat input box": "채팅 입력 상자", "entire chat width": "전체 채팅 폭", "full window width": "전체 창 너비", "STscript Settings": "STscript 설정", "Sets default flags for the STscript parser.": "STscript Parser 기본 플래그 설정", "Parser Flags": "Parser 플래그 설정", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "모든 구분자를 백슬래시로 이스케이핑하고, 백슬래시 자체도 이스케이프할 수 있도록 엄격한 방식으로 전환합니다.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "이중 매크로 대체를 방지하려면 모든 {{getvar::}} 및 {{getglobalvar::}} 매크로를 범위 변수로 바꾸세요.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "배경 이미지 변경", "Filter": "필터", "Automatically select a background based on the chat context": "채팅 컨텍스트를 기반으로 자동으로 배경 선택", "Auto-select": "자동 선택", "System Backgrounds": "시스템 배경", "Chat Backgrounds": "채팅 배경", "bg_chat_hint_1": "다음으로 생성된 채팅 배경", "bg_chat_hint_2": "확장 프로그램이 여기에 표시됩니다.", "Extensions": "확장", "Notify on extension updates": "확장 프로그램 업데이트 알림", "Manage extensions": "확장 프로그램 관리", "Import Extension From Git Repo": "Git 리포지토리에서 확장 프로그램 가져오기", "Install extension": "확장 프로그램 설치", "Extras API:": "외부 API:", "Auto-connect": "자동 연결", "Extras API URL": "엑스트라 API URL", "Extras API key (optional)": "Extras API 키 (선택 사항)", "Persona Management": "페르소나 관리", "How do I use this?": "어떻게 사용하나요?", "Click for stats!": "통계 보기!", "Usage Stats": "사용 통계", "Backup your personas to a file": "개인정보를 파일로 백업합니다.", "Backup": "백업", "Restore your personas from a file": "파일에서 개인정보를 복원합니다.", "Restore": "복원", "Create a dummy persona": "더미 페르소나 만들기", "Create": "만들기", "Toggle grid view": "그리드 보기 전환", "No persona description": "[설명 없음]", "Name": "이름", "Enter your name": "이름을 입력하세요", "Click to set a new User Name": "새 사용자 이름 설정을 저장합니다.", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "선택한 페르소나를 현재 채팅에 잠그려면 클릭하세요. 잠금을 제거하려면 다시 클릭하세요.", "Click to set user name for all messages": "모든 메시지를 현재 선택된 사용자 이름으로 설정하려면 클릭하세요", "Persona Description": "페르소나 설명", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "예: [{{user}}는 28세의 루마니아 고양이 소녀입니다.]", "Tokens persona description": "페르소나 설명 토큰", "Position:": "위치:", "None (disabled)": "없음 (비활성화됨)", "In Story String / Prompt Manager": "이야기 문자열 / 프롬프트 관리자", "Top of Author's Note": "작가 노트 상단", "Bottom of Author's Note": "작가 노트 하단", "In-chat @ Depth": "채팅 내 @ Depth", "Depth:": "깊이:", "Role:": "역할:", "System": "시스템", "User": "사용자", "Assistant": "어시스턴트", "Show notifications on switching personas": "페르소나 전환시 알림 표시", "Character Management": "캐릭터 관리", "Locked = Character Management panel will stay open": "잠금 = 캐릭터 관리 패널이 열린 상태로 유지됩니다", "Select/Create Characters": "캐릭터 선택/생성", "Favorite characters to add them to HotSwaps": "즐겨찾는 캐릭터를 HotSwaps에 추가", "Token counts may be inaccurate and provided just for reference.": "토큰 수가 정확하지 않을 수 있으며 참조 용도로만 제공됩니다.", "Total tokens": "총 토큰", "Calculating...": "계산 중...", "Tokens": "토큰", "Permanent tokens": "영구 토큰", "Permanent": "영구적인", "About Token 'Limits'": "토큰 '한도' 정보", "Toggle character info panel": "캐릭터 정보 패널 전환", "Name this character": "이 캐릭터의 이름을 지정하세요", "extension_token_counter": "토큰:", "Click to select a new avatar for this character": "이 캐릭터의 새 아바타를 선택하려면 클릭하세요", "Add to Favorites": "즐겨찾기에 추가", "Advanced Definition": "고급 정의", "Character Lore": "캐릭터 로어북", "Chat Lore": "채팅 로어북", "Export and Download": "내보내기 및 다운로드", "Duplicate Character": "캐릭터 복제", "Create Character": "캐릭터 생성", "Delete Character": "캐릭터 삭제", "More...": "추가 설정들...", "Link to World Info": "월드 인포에 연결", "Import Card Lore": "카드 로어북 가져오기", "Scenario Override": "시나리오 재정의", "Convert to Persona": "페르소나로 변환", "Rename": "이름 변경", "Link to Source": "소스 링크", "Replace / Update": "교체/업데이트", "Import Tags": "태그 가져오기", "Search / Create Tags": "검색 / 태그 생성", "View all tags": "모든 태그 보기", "Creator's Notes": "제작자의 메모", "Show / Hide Description and First Message": "설명 및 첫 번째 메시지 표시/숨기기", "Character Description": "캐릭터 설명", "Click to allow/forbid the use of external media for this character.": "이 캐릭터에 대한 외부 미디어 사용을 허용/금지하려면 클릭하세요.", "Ext. Media": "외부 미디어", "Describe your character's physical and mental traits here.": "여기에 캐릭터의 신체적, 정신적 특성을 설명하세요.", "First message": "첫 번째 메시지", "Click to set additional greeting messages": "첫 번째 메시지를 추가로 설정하려면 클릭하세요.", "Alt. Greetings": "대체. 첫 메시지", "This will be the first message from the character that starts every chat.": "이것은 모든 채팅을 시작할 때 캐릭터의 첫 번째 메시지가 됩니다.", "Group Controls": "그룹 제어", "Chat Name (Optional)": "채팅 이름 (선택 사항)", "Click to select a new avatar for this group": "이 그룹의 새 아바타를 선택하려면 클릭하세요", "Group reply strategy": "그룹 응답 전략", "Natural order": "자연스러운 순서", "List order": "목록에 배정된 순서", "Group generation handling mode": "그룹 생성 처리 모드", "Swap character cards": "캐릭터 카드 바꾸기", "Join character cards (exclude muted)": "캐릭터 카드 가입 (뮤트 제외)", "Join character cards (include muted)": "캐릭터 카드 가입 (뮤트 포함)", "Inserted before each part of the joined fields.": "결합된 필드의 각 부분 앞에 삽입됩니다.", "Join Prefix": "접두사 가입", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "'캐릭터 카드 합치기'를 선택하면 캐릭터의 모든 해당 필드가 합쳐집니다. 즉, 스토리 문자열에서 예를 들어 모든 캐릭터 설명이 하나의 큰 텍스트로 합쳐집니다. 이러한 필드를 분리하려면 여기에서 접두사나 접미사를 정의할 수 있습니다. 이 값은 일반 매크로를 지원하고 {{char}}를 해당 캐릭터의 이름으로, <FIELDNAME>을 해당 부분의 이름(예: 설명, 성격, 시나리오 등)으로 대체합니다.", "Inserted after each part of the joined fields.": "결합된 필드의 각 부분 뒤에 삽입됩니다.", "Join Suffix": "접미사 가입", "Set a group chat scenario": "그룹 채팅 시나리오 설정", "Click to allow/forbid the use of external media for this group.": "이 그룹에 대한 외부 미디어 사용을 허용/금지하려면 클릭하세요.", "Restore collage avatar": "콜라주 아바타 복원", "Allow self responses": "자체 응답 허용", "Auto Mode": "자동 모드", "Auto Mode delay": "자동 모드 지연", "Hide Muted Member Sprites": "음소거된 멤버 스프라이트 숨기기", "Current Members": "현재 멤버", "Add Members": "멤버 추가", "Create New Character": "새 캐릭터 만들기", "Import Character from File": "파일에서 캐릭터 가져오기", "Import content from external URL": "외부 URL에서 콘텐츠 가져오기", "Create New Chat Group": "새 채팅 그룹 만들기", "Characters sorting order": "캐릭터 정렬 순서", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "최신순", "Oldest": "가장 오래된 순", "Favorites": "즐겨찾기", "Recent": "최근", "Most chats": "대화가 가장 많은", "Least chats": "대화가 가장 적은", "Most tokens": "가장 많은 토큰", "Least tokens": "가장 적은 토큰", "Random": "랜덤", "Toggle character grid view": "캐릭터 그리드 보기 전환", "Bulk_edit_characters": "대량 캐릭터 편집", "Bulk select all characters": "모든 문자 일괄 선택", "Bulk delete characters": "대량 캐릭터 삭제", "popup-button-save": "저장하기", "popup-button-yes": "네", "popup-button-no": "아니요", "popup-button-cancel": "취소", "popup-button-import": "불러오기", "Advanced Definitions": "고급 정의", "Prompt Overrides": "프롬프트 무시", "(For Chat Completion and Instruct Mode)": "(채팅 완료 및 지시 모드의 경우)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "{{original}}를 해당 상자에 넣어 시스템 설정의 기본 프롬프트를 포함합니다.", "Main Prompt": "주요 프롬프트", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "여기에 있는 모든 내용은 이 캐릭터에 사용된 기본 메인 프롬프트를 대체합니다. (v2 사양: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "여기에 있는 모든 내용은 이 캐릭터에 사용된 기본 탈옥 프롬프트를 대체합니다. (v2 사양: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "제작자의 메타데이터 (AI 프롬프트와 함께 전송되지 않음)", "Creator's Metadata": "제작자의 메타데이터", "(Not sent with the AI Prompt)": "(AI 프롬프트와 함께 전송되지 않음)", "Everything here is optional": "여기 있는 모든 것은 선택 사항입니다", "(Botmaker's name / Contact Info)": "(봇 제작자 이름 / 연락처 정보)", "(If you want to track character versions)": "(캐릭터 버전을 추적하려는 경우)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(봇을 설명하고 사용 팁을 제공하거나 테스트된 채팅 모델을 나열합니다. 이 내용은 캐릭터 목록에 표시됩니다.)", "Tags to Embed": "포함할 태그", "(Write a comma-separated list of tags)": "(쉼표로 구분된 태그 목록 작성)", "Personality summary": "성격 요약", "(A brief description of the personality)": "(성격에 대한 간단한 설명)", "Scenario": "시나리오", "(Circumstances and context of the interaction)": "(상호 작용의 상황과 맥락)", "Character's Note": "캐릭터 노트", "(Text to be inserted in-chat @ designated depth and role)": "(채팅에 삽입되는 텍스트@지정된 깊이와 역할)", "@ Depth": "@ 깊이", "Role": "역할", "Talkativeness": "수다스러움", "How often the character speaks in group chats!": "캐릭터가 그룹 채팅에서 얼마나 자주 말할지 설정해보세요!", "How often the character speaks in": "캐릭터가 말하는 빈도", "group chats!": "그룹 채팅!", "Shy": "수줍음", "Normal": "평범함", "Chatty": "수다쟁이", "Examples of dialogue": "대화 예시", "Important to set the character's writing style.": "캐릭터의 글쓰기 스타일을 설정하는 것이 중요합니다.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(채팅 대화의 예제입니다. 각 예제를 새 줄에서 START로 시작하세요.)", "Save": "저장", "Chat History": "채팅 기록", "Import Chat": "채팅 가져오기", "Copy to system backgrounds": "시스템 배경에 복사", "Rename background": "배경 이름 바꾸기", "Lock": "잠금", "Unlock": "잠금 해제", "Delete background": "배경 삭제", "Chat Scenario Override": "채팅 시나리오 재정의", "Remove": "제거", "Type here...": "여기에 입력하세요...", "Chat Lorebook": "채팅 로어북", "Chat Lorebook for": "채팅 로어북", "chat_world_template_txt": "선택한 월드 인포가 이 채팅에 연결됩니다. AI 응답을 생성할 때,\n                    글로벌 및 캐릭터 로어북의 항목과 결합됩니다.", "Select a World Info file for": "다음을 위해 월드 인포 파일 선택:", "Primary Lorebook": "기본 로어북", "A selected World Info will be bound to this character as its own Lorebook.": "선택한 월드 정보가 이 캐릭터에게 자체 로어북으로 바인딩됩니다.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "AI 응답을 생성할 때 전역 월드 정보 선택기의 항목과 결합됩니다.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "캐릭터를 내보내면 JSON 데이터에 포함된 선택한 로어북 파일도 내보냅니다.", "Additional Lorebooks": "추가 로어북", "Associate one or more auxillary Lorebooks with this character.": "하나 이상의 보조 로어북을 이 캐릭터와 연관시킵니다.", "NOTE: These choices are optional and won't be preserved on character export!": "참고: 이 선택 사항은 선택 사항이며 캐릭터 내보내기 시 유지되지 않습니다!", "Rename chat file": "채팅 파일 이름 바꾸기", "Export JSONL chat file": "JSONL 채팅 파일 내보내기", "Download chat as plain text document": "일반 텍스트 문서로 채팅 다운로드", "Delete chat file": "채팅 파일 삭제", "Use tag as folder": "폴더로 태그 지정", "Hide on character card": "캐릭터 카드에서 숨기기", "Delete tag": "태그 삭제", "Entry Title/Memo": "항목 제목/메모", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "WI 입국 상태:\r🔵 상시\r🟢 조건 만족시\r🔗 벡터화됨\r❌ 비활성화", "WI_Entry_Status_Constant": "상시 활성화", "WI_Entry_Status_Normal": "정상", "WI_Entry_Status_Vectorized": "벡터화됨", "WI_Entry_Status_Disabled": "비활성화됨", "T_Position": "↑Char: 캐릭터 정의 전\n↓Char: 캐릭터 정의 후\n↑AN: 작가 노트 전\n↓AN: 작가노트 후\n@D: 깊이", "Before Char Defs": "캐릭터 정의 전", "After Char Defs": "캐릭터 정의 후", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "작가 노트 전", "After AN": "작가 노트 후", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "깊이", "Order:": "순서:", "Order": "순서:", "Trigger %:": "발동 확률 %:", "Probability": "개연성", "Duplicate world info entry": "중복된 월드 인포 항목", "Delete world info entry": "월드 인포 항목 삭제", "Comma separated (required)": "쉼표로 구분 (필수)", "Primary Keywords": "기본 키워드", "Keywords or Regexes": "키워드 또는 정규식", "Comma separated list": "쉼표로 구분된 목록", "Switch to plaintext mode": "일반 텍스트 모드로 전환", "Logic": "논리 구조", "AND ANY": "AND ANY", "AND ALL": "AND ALL", "NOT ALL": "NOT ALL", "NOT ANY": "NOT ANY", "(ignored if empty)": "(비어 있으면 무시됨)", "Optional Filter": "선택적 필터", "Keywords or Regexes (ignored if empty)": "키워드 또는 정규 표현식(비어 있으면 무시됨)", "Comma separated list (ignored if empty)": "쉼표로 구분된 목록(비어 있으면 무시됨)", "Use global setting": "전역 설정 사용", "Case-Sensitive": "대소문자 구분", "Yes": "예", "No": "아니오", "Quick Reply": "빠른 답장", "Can be used to automatically activate Quick Replies": "빠른 답장을 자동으로 활성화하는 데 사용할 수 있습니다.", "Automation ID": "자동화 ID", "( None )": "( 없음 )", "Content": "콘텐츠", "Exclude from recursion": "재귀에서 제외", "Prevent further recursion (this entry will not activate others)": "추가 재귀 방지(이 항목은 다른 항목을 활성화하지 않습니다)", "Delay until recursion (this entry can only be activated on recursive checking)": "재귀까지 지연(이 항목은 재귀 검사에서만 활성화될 수 있음)", "What this keyword should mean to the AI, sent verbatim": "AI에게 이 키워드가 무엇을 의미하는지를 원래 그대로 보냄", "Filter to Character(s)": "캐릭터로 필터링", "Character Exclusion": "캐릭터 제외", "-- Characters not found --": "-- 캐릭터를 찾을 수 없음 --", "Inclusion Group": "포함 그룹", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "포함 그룹은 여러 항목이 트리거되는 경우 그룹에서 한 번에 하나의 항목만 활성화되도록 합니다.\r쉼표로 구분된 여러 그룹을 지원합니다.\r\r문서: 월드 인포 - 포함 그룹", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "이 항목 우선순위 지정: 선택하면 모든 선택 항목 중에서 이 항목의 우선순위가 지정됩니다.\r여러 항목의 우선순위가 높은 경우 '순서'가 가장 높은 항목이 선택됩니다.", "Only one entry with the same label will be activated": "동일한 라벨을 가진 항목은 하나만 활성화됩니다", "A relative likelihood of entry activation within the group": "그룹 내 항목 활성화의 상대적 가능성", "Group Weight": "그룹 가중치", "Selective": "선택적", "Use Probability": "확률 사용", "Add Memo": "메모 추가", "Text or token ids": "텍스트 또는 [토큰 ID]", "close": "닫다", "prompt_manager_edit": "프롬프트 편집", "prompt_manager_name": "이름", "A name for this prompt.": "이 프롬프트의 이름입니다.", "To whom this message will be attributed.": "해당 프롬프트에 부여할 역할은 무엇인가요?", "AI Assistant": "AI 어시스턴트", "prompt_manager_position": "위치", "Next to other prompts (relative) or in-chat (absolute).": "다른 프롬프트 옆(상대적) 또는 채팅 내(절대적).", "prompt_manager_relative": "상대적인", "prompt_manager_in_chat": "깊이에 따라", "prompt_manager_depth": "깊이", "0 = after the last message, 1 = before the last message, etc.": "0 = 마지막 메시지 뒤, 1 = 마지막 메시지 앞 등", "Prompt": "프롬프트", "The prompt to be sent.": "보내질 프롬프트 내용을 작성하는 부분입니다.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "이 프롬프트는 고급 정의에서 재정의가 선호되는 경우에도 재정의될 수 없습니다.", "prompt_manager_forbid_overrides": "재정의 금지", "reset": "초기화", "save": "저장", "This message is invisible for the AI": "이 메시지는 AI에게 보이지 않습니다", "Message Actions": "메시지 작업", "Translate message": "메시지 번역", "Generate Image": "이미지 생성", "Narrate": "나레이션 하기", "Exclude message from prompts": "프롬프트에서 메시지 제외", "Include message in prompts": "프롬프트에 메시지 포함", "Embed file or image": "파일 또는 이미지 삽입", "Create checkpoint": "체크포인트 만들기", "Create Branch": "분기 생성", "Copy": "복사", "Open checkpoint chat": "체크포인트 채팅 오픈", "Edit": "편집", "Confirm": "확인", "Copy this message": "이 메시지 복사", "Delete this message": "이 메시지 삭제", "Move message up": "메시지를 위로 이동", "Move message down": "메시지를 아래로 이동", "Enlarge": "확대", "Welcome to SillyTavern!": "SillyTavern에 오신 것을 환영합니다!", "welcome_message_part_1": "자세한 설명 읽기:", "welcome_message_part_2": "공식 문서", "welcome_message_part_3": null, "welcome_message_part_4": "유형", "welcome_message_part_5": "명령과 매크로에 대한 채팅.", "welcome_message_part_6": "공식", "Discord server": "디스코드 서버", "welcome_message_part_7": "에서 정보 및 공지사항을 확인하세요.", "SillyTavern is aimed at advanced users.": "SillyTavern은 고급 사용자를 대상으로 합니다.", "If you're new to this, enable the simplified UI mode below.": "처음 사용하는 경우 아래의 단순화된 UI 모드를 활성화하세요.", "Change it later in the 'User Settings' panel.": "나중에 '사용자 설정' 패널에서 변경하세요.", "Enable simple UI mode": "단순 UI 모드 활성화", "Looking for AI characters?": "AI 캐릭터를 찾고 계십니까?", "onboarding_import": "가져오기", "from supported sources or view": "지원되는 소스 또는 보기에서", "Sample characters": "샘플 캐릭터", "Your Persona": "당신의 페르소나", "Before you get started, you must select a persona name.": "시작하기 전에 페르소나 이름을 선택해야 합니다.", "welcome_message_part_8": "이는 언제든지 변경될 수 있습니다. 변경을 원한다면", "welcome_message_part_9": "아이콘을 누르세요.", "Persona Name:": "페르소나 이름:", "Temporarily disable automatic replies from this character": "이 캐릭터의 자동 응답 일시적으로 비활성화", "Enable automatic replies from this character": "이 캐릭터의 자동 응답 활성화", "Trigger a message from this character": "이 캐릭터에서 메시지 트리거", "Move up": "위로 이동", "Move down": "아래로 이동", "View character card": "캐릭터 카드 보기", "Remove from group": "그룹에서 제거", "Add to group": "그룹에 추가", "Alternate Greetings": "대체 인사말", "Alternate_Greetings_desc": "이는 새 채팅을 시작할 때 첫 번째 메시지에 스와이프로 표시됩니다.\n                그룹 구성원은 그 중 하나를 선택하여 대화를 시작할 수 있습니다.", "Alternate Greetings Hint": "시작하려면 버튼을 클릭하세요!", "(This will be the first message from the character that starts every chat)": "(이것은 모든 채팅을 시작하는 캐릭터의 첫 번째 메시지가 됩니다)", "Forbid Media Override explanation": "현재 캐릭터/그룹이 채팅에서 외부 미디어를 사용할 수 있는 능력입니다.", "Forbid Media Override subtitle": "미디어: 이미지, 비디오, 오디오. 외부: 로컬 서버에서 호스팅되지 않습니다.", "Always forbidden": "항상 금지", "Always allowed": "항상 허용됨", "View contents": "내용 보기", "Remove the file": "파일을 제거하세요", "Unique to this chat": "이 채팅에만 고유함", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "체크포인트는 상위 항목으로부터 메모를 상속받으며 이후 개별적으로 변경될 수 있습니다.", "Include in World Info Scanning": "월드인포를 스캔 대상에 포함", "Before Main Prompt / Story String": "메인 프롬프트 전/스토리 문자열", "After Main Prompt / Story String": "메인 프롬프트 이후 / 스토리 문자열", "as": "~처럼", "Insertion Frequency": "삽입 빈도", "(0 = Disable, 1 = Always)": "(0 = 비활성화, 1 = 항상)", "User inputs until next insertion:": "다음 삽입까지 사용자 입력:", "Character Author's Note (Private)": "캐릭터 작가의 메모 (비공개)", "Won't be shared with the character card on export.": "내보내기 시 캐릭터 카드와 공유되지 않습니다.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "해당 캐릭터에 대한 작가 노트로 자동 추가됩니다. 그룹으로 사용되지만\n                            그룹 채팅이 열려 있으면 수정할 수 없습니다.", "Use character author's note": "캐릭터 작가 노트 사용", "Replace Author's Note": "작가 노트 자리를 대체하기", "Default Author's Note": "기본 작가 노트", "Will be automatically added as the Author's Note for all new chats.": "모든 새 채팅에 대한 작가 노트로 자동으로 추가됩니다.", "Chat CFG": "채팅CFG", "1 = disabled": "1 = 비활성화됨", "write short replies, write replies using past tense": "짧은 답장 쓰기, 과거 시제를 사용하여 답장 쓰기", "Positive Prompt": "긍정적 프롬프트", "Use character CFG scales": "캐릭터 CFG 스케일 사용", "Character CFG": "캐릭터 CFG", "Will be automatically added as the CFG for this character.": "해당 캐릭터의 CFG로 자동 추가됩니다.", "Global CFG": "글로벌 CFG", "Will be used as the default CFG options for every chat unless overridden.": "재정의되지 않는 한 모든 채팅에 대해 기본 CFG 옵션으로 사용됩니다.", "CFG Prompt Cascading": "CFG 프롬프트 세분화", "Combine positive/negative prompts from other boxes.": "다른 상자의 긍정적/부정적 프롬프트를 결합합니다.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "예를 들어, 채팅, 글로벌, 문자 상자를 체크하면 모든 부정 프롬프트가 쉼표로 구분된 문자열로 결합됩니다.", "Always Include": "항상 포함", "Chat Negatives": "채팅 부정적인 내용", "Character Negatives": "문자 네거티브", "Global Negatives": "글로벌 네거티브", "Custom Separator:": "맞춤 구분 기호:", "Insertion Depth:": "삽입 깊이:", "Token Probabilities": "토큰 확률", "Select a token to see alternatives considered by the AI.": "AI가 고려하는 대안을 보려면 토큰을 선택하세요.", "Not connected to API!": "API에 연결되지 않았습니다!", "Type a message, or /? for help": "메시지를 입력하거나 /? 도와주기 위해", "Continue script execution": "스크립트 실행 계속", "Pause script execution": "스크립트 실행 일시 중지", "Abort script execution": "스크립트 실행 중단", "Abort request": "요청 중단", "Continue the last message": "마지막 메시지 계속하기", "Send a message": "메시지 보내기", "Close chat": "채팅 닫기", "Toggle Panels": "토글 패널", "Back to parent chat": "부모 채팅으로 돌아가기", "Save checkpoint": "체크포인트 저장", "Convert to group": "그룹 채팅으로 변환", "Start new chat": "새로운 채팅 시작", "Manage chat files": "채팅 파일 관리", "Delete messages": "메시지 삭제", "Regenerate": "재생성", "Ask AI to write your message for you": "AI에게 메시지를 대신 작성하도록 요청", "Impersonate": "사칭", "Continue": "계속 생성하기", "Bind user name to that avatar": "그 아바타에 사용자 이름 바인딩", "Change persona image": "페르소나 이미지 변경", "Select this as default persona for the new chats.": "새 채팅의 기본 페르소나로 선택합니다.", "Delete persona": "페르소나 삭제", "These characters are the winners of character design contests and have outstandable quality.": "캐릭터 디자인 공모전에서 당선된 캐릭터로, 뛰어난 퀄리티를 자랑하는 캐릭터입니다.", "Contest Winners": "콘테스트 우승자", "These characters are the finalists of character design contests and have remarkable quality.": "이 캐릭터들은 캐릭터 디자인 공모전의 최종 후보로, 뛰어난 퀄리티를 자랑합니다.", "Featured Characters": "독립된 캐릭터", "Attach a File": "파일 첨부하기", "Open Data Bank": "오픈데이터뱅크", "Enter a URL or the ID of a Fandom wiki page to scrape:": "스크랩할 Fandom 위키 페이지의 URL이나 ID를 입력하세요:", "Examples:": "예:", "Example:": "예:", "Single file": "단일 파일", "All articles will be concatenated into a single file.": "모든 기사는 단일 파일로 연결됩니다.", "File per article": "기사당 파일", "Each article will be saved as a separate file.": "권장되지 않습니다. 각 기사는 별도의 파일로 저장됩니다.", "Data Bank": "데이터 저장소", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "이러한 파일은 첨부 파일을 지원하는 확장 프로그램(예: 벡터 저장소)에 사용할 수 있습니다.", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "지원되는 파일 유형: 일반 텍스트, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "여기에 파일을 끌어다 놓아 업로드하세요.", "Date (Newest First)": "날짜(최신순)", "Date (Oldest First)": "날짜(오래된 것부터)", "Name (A-Z)": "이름(A~Z)", "Name (Z-A)": "이름(Z-A)", "Size (Smallest First)": "크기(가장 작은 것부터)", "Size (Largest First)": "크기(큰 것부터)", "Bulk Edit": "대량 편집", "Select All": "모두 선택", "Select None": "없음을 선택하세요", "Global Attachments": "전역 첨부", "These files are available for all characters in all chats.": "이 파일은 모든 채팅의 모든 캐릭터에 사용할 수 있습니다.", "Character Attachments": "캐릭터 부착물", "These files are available the current character in all chats they are in.": "이 파일은 해당 파일이 속한 모든 채팅에서 현재 캐릭터에서 사용할 수 있습니다.", "Saved locally. Not exported.": "로컬에 저장됨. 내보내지 않음.", "Chat Attachments": "채팅 첨부파일", "These files are available to all characters in the current chat.": "이 파일은 현재 채팅의 모든 캐릭터가 사용할 수 있습니다.", "Enter a base URL of the MediaWiki to scrape.": "스크래핑할 미디어위키의 기본 URL을 입력하세요.", "Don't include the page name!": "페이지 이름을 포함하지 마세요!", "Enter web URLs to scrape (one per line):": "스크랩할 웹 URL을 입력하세요(한 줄에 하나씩):", "Enter a video URL to download its transcript.": "스크립트를 다운로드하려면 동영상 URL 또는 ID를 입력하세요.", "Expression API": "표현\n엑스트라\nAPI", "ext_sum_current_summary": "현재 요약:", "ext_sum_restore_previous": "이전 복원", "ext_sum_memory_placeholder": "여기에 요약이 생성됩니다...", "Trigger a summary update right now.": "지금 요약하세요", "ext_sum_force_text": "지금 요약하세요", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "자동 요약 업데이트를 비활성화합니다. 일시 중지된 동안 요약은 그대로 유지됩니다. 지금 요약 버튼(기본 API에서만 사용 가능)을 눌러 강제로 업데이트할 수 있습니다.", "ext_sum_pause": "요약 정지시키기", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "요약할 텍스트에서 World Info 및 Author's Note를 생략합니다. Main API를 사용할 때만 효과가 있습니다. Extras API는 항상 WI/AN을 생략합니다.", "ext_sum_no_wi_an": "WI/AN 없음", "ext_sum_settings_tip": "요약 프롬프트, 삽입 위치 등을 편집합니다.", "ext_sum_settings": "요약 설정", "ext_sum_prompt_builder": "프롬프트 빌더", "ext_sum_prompt_builder_1_desc": "확장 프로그램은 아직 요약되지 않은 메시지를 사용하여 자체 프롬프트를 작성합니다. 요약이 생성될 때까지 채팅을 차단합니다.", "ext_sum_prompt_builder_1": "원시, 차단", "ext_sum_prompt_builder_2_desc": "확장 프로그램은 아직 요약되지 않은 메시지를 사용하여 자체 프롬프트를 작성합니다. 요약이 생성되는 동안 채팅을 차단하지 않습니다. 하지만 모든 백엔드가 이 모드를 지원하는 것은 아닙니다.", "ext_sum_prompt_builder_2": "원시, 비차단", "ext_sum_prompt_builder_3_desc": "확장은 일반 기본 프롬프트 빌더를 사용하고 요약 요청을 마지막 시스템 메시지로 추가합니다.", "ext_sum_prompt_builder_3": "클래식, 차단", "Summary Prompt": "요약 프롬프트", "ext_sum_restore_default_prompt_tip": "기본 프롬프트 복원", "ext_sum_prompt_placeholder": "이 프롬프트는 요약 생성을 요청하기 위해 AI로 전송됩니다. {{words}}는 '단어 수' 매개변수로 해석됩니다.", "ext_sum_target_length_1": "목표 요약 길이", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "단어)", "ext_sum_api_response_length_1": "API 응답 길이", "ext_sum_api_response_length_2": "(( ...", "ext_sum_api_response_length_3": "토큰)", "ext_sum_0_default": "0 = 기본값", "ext_sum_raw_max_msg": "[원시] 요청당 최대 메시지 수", "ext_sum_0_unlimited": "0 = 무제한", "Update frequency": "업데이트 빈도", "ext_sum_update_every_messages_1": "업데이트 간격", "ext_sum_update_every_messages_2": "메시지", "ext_sum_0_disable": "0 = 비활성화", "ext_sum_auto_adjust_desc": "채팅 지표에 따라 간격을 자동으로 조정해 보세요.", "ext_sum_update_every_words_1": "매 업데이트", "ext_sum_update_every_words_2": "단어", "ext_sum_both_sliders": "두 슬라이더가 모두 0이 아닌 경우 둘 다 해당 간격으로 요약 업데이트를 트리거합니다.", "ext_sum_injection_template": "주입 템플릿", "ext_sum_memory_template_placeholder": "{{summary}}는 현재 요약 내용으로 해석됩니다.", "ext_sum_injection_position": "주입 위치", "How many messages before the current end of the chat.": "현재 채팅이 끝나기 전의 메시지 수입니다.", "ext_regex_title": "정규식", "ext_regex_new_global_script": "+ 글로벌", "ext_regex_new_scoped_script": "+ 범위 지정", "ext_regex_import_script": "불러오기", "ext_regex_global_scripts": "글로벌 스크립트", "ext_regex_global_scripts_desc": "모든 캐릭터에 사용 가능합니다. 로컬 설정에 저장되었습니다.", "ext_regex_scoped_scripts": "범위가 지정된 스크립트", "ext_regex_scoped_scripts_desc": "해당 캐릭터에만 사용 가능합니다. 카드 데이터에 저장됩니다.", "Regex Editor": "정규식 편집기", "Test Mode": "테스트 모드", "ext_regex_desc": "Regex는 정규식을 사용하여 문자열을 찾거나 바꾸는 도구입니다. 자세한 내용을 보려면 ?를 클릭하세요. 제목 옆에.", "Input": "입력", "ext_regex_test_input_placeholder": "여기에 입력하세요...", "Output": "산출", "ext_regex_output_placeholder": "비어 있는", "Script Name": "스크립트 이름", "Find Regex": "정규식 찾기", "Replace With": "다음으로 바꾸기", "ext_regex_replace_string_placeholder": "정규 표현식 찾기 또는 캡처 그룹의 경우 $1, $2 등에서 일치하는 텍스트를 포함하려면 {{match}}를 사용하세요.", "Trim Out": "트림아웃", "ext_regex_trim_placeholder": "교체하기 전에 정규식 일치에서 원하지 않는 부분을 전체적으로 잘라냅니다. 각 요소를 Enter로 구분하세요.", "ext_regex_affects": "영향을 미침", "ext_regex_user_input": "사용자 입력", "ext_regex_ai_output": "AI 출력", "Slash Commands": "슬래시 명령", "ext_regex_min_depth_desc": "프롬프트 또는 표시에 적용하면 최소 N 수준 깊이의 메시지에만 영향을 미칩니다. 0 = 마지막 메시지, 1 = 끝에서 두 번째 메시지 등. WI 항목 @Depth 및 사용 가능한 메시지만 계산합니다. 즉, 숨겨진 메시지나 시스템 메시지는 계산하지 않습니다.", "Min Depth": "최소 깊이", "ext_regex_min_depth_placeholder": "제한 없는", "ext_regex_max_depth_desc": "프롬프트나 표시에 적용하면 N 수준 이하의 메시지에만 영향을 미칩니다. 0 = 마지막 메시지, 1 = 끝에서 두 번째 메시지 등. WI 항목 @Depth 및 사용 가능한 메시지만 계산합니다. 즉, 숨겨진 메시지나 시스템 메시지는 계산하지 않습니다.", "ext_regex_other_options": "다른 옵션", "Only Format Display": "형식 표시만", "ext_regex_only_format_prompt_desc": "채팅 기록은 변경되지 않으며 요청이 전송될 때 메시지만 생성됩니다.", "Only Format Prompt (?)": "형식 프롬프트만", "Run On Edit": "편집 실행", "ext_regex_substitute_regex_desc": "실행하기 전에 Find Regex에서 {{macros}}를 대체하세요.", "Substitute Regex": "정규식 대체", "ext_regex_import_target": "가져오기 대상:", "ext_regex_disable_script": "스크립트 비활성화", "ext_regex_enable_script": "스크립트 활성화", "ext_regex_edit_script": "스크립트 편집", "ext_regex_move_to_global": "전역 스크립트로 이동", "ext_regex_move_to_scoped": "범위가 지정된 스크립트로 이동", "ext_regex_export_script": "스크립트 내보내기", "ext_regex_delete_script": "스크립트 삭제", "Trigger Stable Diffusion": "트리거 안정 확산", "sd_Yourself": "당신 자신", "sd_Your_Face": "너의 얼굴", "sd_Me": "나", "sd_The_Whole_Story": "전체 스토리", "sd_The_Last_Message": "마지막 메시지", "sd_Raw_Last_Message": "원시 마지막 메시지", "sd_Background": "배경", "Image Generation": "이미지 생성", "sd_refine_mode": "프롬프트를 생성 API로 보내기 전에 수동으로 편집할 수 있도록 허용", "sd_refine_mode_txt": "생성 전에 프롬프트 편집", "sd_interactive_mode": "'고양이 사진 보내주세요'와 같은 메시지를 보낼 때 자동으로 이미지를 생성합니다.", "sd_interactive_mode_txt": "대화에 따른 자동 이미지 생성 모드", "sd_multimodal_captioning": "다중 모달 캡션을 사용하여 아바타를 기반으로 사용자 및 캐릭터 초상화에 대한 프롬프트를 생성합니다.", "sd_multimodal_captioning_txt": "인물 사진에 다중 모드 캡션 사용", "sd_expand": "텍스트 생성 모델을 사용하여 프롬프트 자동 확장", "sd_expand_txt": "자동 강화 프롬프트", "sd_snap": "강제 종횡비(인물 사진, 배경)를 사용하여 가장 가까운 알려진 해상도로 스냅 생성을 요청하는 동시에 절대 픽셀 수를 유지하려고 합니다(SDXL에 권장).", "sd_snap_txt": "스냅 자동 조정 해상도", "Source": "근원", "Horde": "Horde", "sd_auto_url": "예: {{auto_url}}", "Authentication (optional)": "인증(선택사항)", "Example: username:password": "예: 사용자 이름:비밀번호", "Important:": "중요한:", "sd_auto_auth_warning_1": "SD 웹 UI를 실행하세요", "sd_auto_auth_warning_2": "Flag! SillyTavern 호스트 시스템에서 서버에 액세스할 수 있어야 합니다.", "sd_drawthings_url": "예: {{drawthings_url}}", "sd_drawthings_auth_txt": "UI에서 HTTP API 스위치가 활성화된 상태에서 DrawThings 앱을 실행하세요! SillyTavern 호스트 시스템에서 서버에 액세스할 수 있어야 합니다.", "sd_vlad_url": "예: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "SillyTavern 호스트 시스템에서 서버에 액세스할 수 있어야 합니다.", "Hint: Save an API key in AI Horde API settings to use it here.": "힌트: 여기에서 사용하려면 AI Horde API 설정에 API 키를 저장하세요.", "Allow NSFW images from Horde": "Horde의 NSFW 이미지 허용", "Sanitize prompts (recommended)": "프롬프트 삭제(권장)", "Automatically adjust generation parameters to ensure free image generations.": "무료 이미지 생성을 보장하기 위해 생성 매개변수를 자동으로 조정합니다.", "Avoid spending Anlas": "<PERSON><PERSON> 지출을 피하세요", "Opus tier": "(오푸스 티어)", "View my Anlas": "내 <PERSON><PERSON> 보기", "These settings only apply to DALL-E 3": "이 설정은 DALL-E 3에만 적용됩니다.", "Image Style": "이미지 스타일", "Image Quality": "이미지 품질", "Standard": "기준", "HD": "HD(고화질)", "sd_comfy_url": "예: {{comfy_url}}", "Open workflow editor": "워크플로우로 편집기 열기", "Create new workflow": "새 워크플로우 만들기", "Delete workflow": "워크플로우 삭제", "Enhance": "향상시키다", "Refine": "구체화", "Decrisper": "더 명확하게", "Sampling steps": "샘플링 단계 ()", "Width": "너비 ()", "Height": "높이 ()", "Resolution": "해상도", "Model": "모델", "Ask every time": "매번 물어보기", "Sampling method": "샘플링 방법", "Message Template": "메시지 템플릿", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON>(일부 샘플러는 지원되지 않음)", "SMEA versions of samplers are modified to perform better at high resolution.": "샘플러의 SMEA 버전은 고해상도에서 더 나은 성능을 발휘하도록 수정되었습니다.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "SMEA 샘플러의 DYN 변형은 종종 더 다양한 출력으로 이어지지만 매우 높은 해상도에서는 실패할 수 있습니다.", "DYN": "DYN", "Scheduler": "스케줄러", "Restore Faces": "얼굴 디테일 복원", "Hires. Fix": "고해상도 업스케일", "Upscaler": "업스케일러", "Upscale by": "고급화", "Denoising strength": "노이즈 제거 강도", "Hires steps (2nd pass)": "고용 단계(2차 패스)", "Preset for prompt prefix and negative prompt": "프롬프트 접두사 및 부정적인 프롬프트에 대한 사전 설정", "Style": "스타일", "Save style": "스타일 저장", "Delete style": "스타일 삭제", "Common prompt prefix": "공통 프롬프트 접두사", "sd_prompt_prefix_placeholder": "생성된 프롬프트가 삽입될 위치를 지정하려면 {prompt}를 사용하세요.", "Negative common prompt prefix": "부정 공통 프롬프트 접두사", "Character-specific prompt prefix": "문자별 프롬프트 접두사", "Won't be used in groups.": "그룹에서는 사용되지 않습니다.", "sd_character_prompt_placeholder": "현재 선택된 캐릭터를 설명하는 모든 특성입니다. 공통 프롬프트 접두사 뒤에 추가됩니다.\n예: 여성, 녹색 눈, 갈색 머리, 분홍색 셔츠", "Character-specific negative prompt prefix": "문자별 부정적인 프롬프트 접두사", "sd_character_negative_prompt_placeholder": "선택한 캐릭터에 나타나서는 안 되는 특성입니다. 부정적인 공통 프롬프트 접두사 뒤에 추가됩니다.\n예: 보석, 신발, 안경", "Shareable": "공유 가능", "Image Prompt Templates": "이미지 프롬프트 템플릿", "Vectors Model Warning": "채팅 중에 모델을 변경할 때 벡터를 제거하는 것이 좋습니다. 그렇지 않으면 하위 수준의 결과가 발생합니다.", "Translate files into English before processing": "처리하기 전에 파일을 영어로 번역하세요", "Manager Users": "사용자 관리", "New User": "새로운 사용자", "Status:": "상태:", "Created:": "만들어진:", "Display Name:": "이름 표시하기:", "User Handle:": "사용자 핸들:", "Password:": "비밀번호:", "Confirm Password:": "비밀번호 확인:", "This will create a new subfolder...": "이렇게 하면 사용자 핸들을 폴더 이름으로 사용하여 /data/ 디렉터리에 새 하위 폴더가 생성됩니다.", "Current Password:": "현재 비밀번호:", "New Password:": "새 비밀번호:", "Confirm New Password:": "새 암호를 확인합니다:", "Debug Warning": "이 범주의 기능은 고급 사용자만을 위한 것입니다. 결과가 확실하지 않으면 아무 것도 클릭하지 마세요.", "Execute": "실행하다", "Are you sure you want to delete this user?": "이 사용자를 삭제하시겠습니까?", "Deleting:": "삭제 중:", "Also wipe user data.": "사용자 데이터도 삭제하세요.", "Warning:": "경고:", "This action is irreversible.": "이 작업은 되돌릴 수 없습니다.", "Type the user's handle below to confirm:": "확인하려면 아래에 사용자 핸들을 입력하세요.", "Import Characters": "캐릭터 가져오기", "Enter the URL of the content to import": "가져올 콘텐츠의 URL을 입력하세요.", "Supported sources:": "지원되는 소스:", "char_import_1": "Chub 캐릭터 (다이렉트 링크 또는 ID)", "char_import_example": "예:", "char_import_2": "Chub Lorebook(직접 링크 또는 ID)", "char_import_3": "JanitorAI 캐릭터(직접 링크 또는 UUID)", "char_import_4": "Pygmalion.chat 문자(직접 링크 또는 UUID)", "char_import_5": "AICharacterCards.com 캐릭터(직접 링크 또는 ID)", "char_import_6": "직접 PNG 링크(참조", "char_import_7": "허용된 호스트의 경우)", "char_import_8": "RisuRealm 캐릭터 (직접링크)", "char_import_9": "Soulkyn 캐릭터 (직접링크)", "char_import_10": "Perchance 캐릭터 (직접 링크 또는 UUID + .gz)", "Supports importing multiple characters.": "여러 문자 가져오기를 지원합니다.", "Write each URL or ID into a new line.": "각 URL 또는 ID를 새 줄에 작성합니다.", "Export for character": "캐릭터 내보내기", "Export prompts for this character, including their order.": "내보내기는 순서를 포함하여 이 캐릭터에 대한 프롬프트를 표시합니다.", "Export all": "모두 내보내기", "Export all your prompts to a file": "모든 프롬프트를 파일로 내보내기", "Insert prompt": "프롬프트 삽입", "Delete prompt": "프롬프트 삭제", "Import a prompt list": "프롬프트 목록 가져오기", "Export this prompt list": "이 프롬프트 목록 내보내기", "Reset current character": "현재 캐릭터 재설정", "New prompt": "새로운 프롬프트", "Prompts": "프롬프트", "Total Tokens:": "총 토큰:", "prompt_manager_tokens": "토큰", "Are you sure you want to reset your settings to factory defaults?": "설정을 공장 기본값으로 재설정하시겠습니까?", "Don't forget to save a snapshot of your settings before proceeding.": "계속하기 전에 설정의 스냅샷을 저장하는 것을 잊지 마세요.", "Settings Snapshots": "설정 스냅샷", "Record a snapshot of your current settings.": "현재 설정의 스냅샷을 기록하세요.", "Make a Snapshot": "스냅샷 만들기", "Restore this snapshot": "이 스냅샷 복원", "Hi,": "안녕,", "To enable multi-account features, restart the SillyTavern server with": "다중 계정 기능을 활성화하려면 SillyTavern 서버를 다시 시작하세요.", "set to true in the config.yaml file.": "config.yaml 파일에서 true로 설정됩니다.", "Account Info": "계정 정보", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "사용자 아바타를 변경하려면 아래 버튼을 사용하거나 페르소나 관리 메뉴에서 기본 페르소나를 선택하세요.", "Set your custom avatar.": "맞춤형 아바타를 설정하세요.", "Remove your custom avatar.": "맞춤 아바타를 삭제하세요.", "Handle:": "핸들:", "This account is password protected.": "이 계정은 비밀번호로 보호되어 있습니다.", "This account is not password protected.": "이 계정은 비밀번호로 보호되어 있지 않습니다.", "Account Actions": "계정 작업", "Change Password": "비밀번호 변경", "Manage your settings snapshots.": "설정 스냅샷을 관리하세요.", "Download a complete backup of your user data.": "사용자 데이터의 전체 백업을 다운로드하세요.", "Download Backup": "백업 다운로드", "Danger Zone": "위험지대", "Reset your settings to factory defaults.": "설정을 공장 기본값으로 재설정하세요.", "Reset Settings": "설정 초기화", "Wipe all user data and reset your account to factory settings.": "모든 사용자 데이터를 지우고 계정을 공장 설정으로 재설정하세요.", "Reset Everything": "모든 것을 재설정", "Reset Code:": "재설정 코드:", "Want to update?": "업데이트 하시겠습니까?", "How to start chatting?": "어떻게 채팅을 시작할 수 있나요?", "Click _space": "해당 아이콘을 클릭하세요.", "and connect to an": "그리고 API를 해당 탭에서 연결하세요.", "and select a": "그리고 선택하세요.", "Chat API": "채팅 API", "and pick a character.": "그리고 원하는 캐릭터를 선택하세요.", "You can browse a list of bundled characters in the": "번들 캐릭터 목록을 검색할 수 있습니다.", "Download Extensions & Assets": "확장 프로그램 및 에셋 다운로드", "Character Expressions": "캐릭터 감정 표현들", "ext_translate_title": "채팅 번역", "ext_translate_auto_mode": "자동 번역 모드", "ext_translate_mode_none": "없음", "ext_translate_mode_responses": "응답 자동 번역", "ext_translate_mode_inputs": "입력 자동 번역", "ext_translate_mode_both": "응답과 입력 모두 번역", "ext_translate_mode_provider": "번역 공급자", "ext_translate_target_lang": "번역 도착 언어", "ext_translate_clear": "번역 지우기", "ext_sum_title": "채팅 요약", "ext_sum_with": "요약에 사용될 API:", "ext_sum_main_api": "메인 API", "Vector Storage": "벡터 저장소", "menu within": "내의 메뉴", "Translate text to English before classification": "분류 전에 텍스트를 영어로 번역합니다.", "Show default images (emojis) if sprite missing": "해당하는 스프라이트가 없으면 기본 이미지 (이모지들)을 표시합니다.", "Classifier API": "분류를 위한 API", "Select the API for classifying expressions.": "감정 이미지들을 분류할 API를 선택하세요.", "Local": "로컬", "Extras": "외부", "LLM": "대형 언어 모델(LLM)", "LLM Prompt": "LLM 프롬프트", "Will be used if the API doesn't support JSON schemas or function calling.": "API가 JSON 스키마나 함수 호출을 지원하지 않는 경우 사용됩니다.", "Default / Fallback Expression": "기본 / 대체 감정들", "Set the default and fallback expression being used when no matching expression is found.": "일치하는 표현이 없을 때 사용되는 기본 및 대체 표현을 설정합니다.", "Custom Expressions": "커스텀 감정들", "Can be set manually or with an _space": "수동으로 설정하거나 또는", "space_ slash command.": "슬래시 명령어로 설정할 수 있습니다.", "Open a chat to see the character expressions.": "캐릭터 감정을 보려면 채팅창을 열어주세요.", "Sprite Folder Override": "스프라이트 폴더 덮어쓰기", "Upload sprite pack (ZIP)": "스프라이트 압축팩 (ZIP) 업로드하기", "Remove all image overrides": "모든 이미지 재정의 제거하기", "Hint:": "힌트:", "Create new folder in the _space": "사용자 데이터 경로에 있는", "folder of your user data directory and name it as the name of the character.": "폴더에 새 폴더를 만들고 해당 폴더의 이름을 캐릭터의 이름으로 지정하세요.", "Put images with expressions there. File names should follow the pattern:": "그리고 그곳에 표정이 담긴 이미지를 넣으세요. 파일 이름은 다음 패턴을 따라야 합니다:", "Sprite set:": "스프라이트 세트:", "sd_free_extend_txt": "무료 모드 프롬프트 확장", "Confused or lost?": "혼란스러우시거나 길을 잃으셨나요?", "click these icons!": "이 아이콘을 클릭하세요!", "in the chat bar": "채팅 바에서", "SillyTavern Documentation Site": "SillyTavern 공식 문서 사이트", "Extras Installation Guide": "Extras 설치 안내서", "Still have questions?": "아직 질문이 있으신가요?", "Join the SillyTavern Discord": "SillyTavern 디스코드에 참여하세요", "Post a GitHub issue": "GitHub 이슈 게시", "Contact the developers": "개발자에게 연락하기", "enable_functions_desc_1": "다양한 확장 프로그램에서 추가 기능을 제공하기 위한", "enable_functions_desc_2": "기능 도구", "enable_functions_desc_3": "를 사용할 수 있게 합니다.", "Relative (to other prompts in prompt manager) or In-chat @ Depth.": "상대적인 (프롬프트 관리 목록에 있는 다른 프롬프트들에 비해) 또는 @Depth 깊이에 따라.", "Instruct Template": "지시 템플릿", "System Message Sequences": "시스템 메시지 시퀀스", "System Prompt Sequences": "시스템 프롬프트 시퀀스", "First User Prefix": "첫 번째 유저 접두사", "Last User Prefix": "마지막 유저 접두사", "System Prefix": "시스템 메시지 접두사", "System Suffix": "시스템 메시지 접미사", "Assistant Message Sequences": "어시스턴트 메시지 시퀀스", "Assistant Prefix": "어시스턴트 메시지 접두사", "Assistant Suffix": "어시스턴트 메시지 접미사", "User Message Sequences": "유저 메시지 시퀀스", "User Prefix": "유저 메시지 접미사", "User Suffix": "유저 메시지 접두사", "Groups and Past Personas": "그룹과 과거 페르소나들", "Never": "절대 추가하지 않음", "Always": "항상 추가함", "Separators as Stop Strings": "구분 기호를 정지 문자열로 사용하기", "Names as Stop Strings": "캐릭터의 이름들을 정지 문자열로 사용하기", "Image Captioning": "이미지 캡셔닝", "Automatically caption images": "자동으로 이미지에 대한 설명 문장으로 나타내기", "Edit captions before saving": "저장하기 전에 이미지에 대한 설명 문장 편집하기", "Enable Quick Replies": "빠른 답장 활성화", "Combine Quick Replies": "빠른 답장 결합하기", "Show Popout Button": "팝아웃 버튼 표시 (데스크톱에서)", "Global Quick Reply Sets": "글로벌 빠른 답장 세트들", "Edit Quick Replies": "빠른 답변 편집하기", "Disable send (insert into input field)": "보내기 비활성화 (입력 필드에 삽입)", "Place quick reply before input": "인풋 전에 빠른 답변 위치하게 하기", "Inject user input automatically": "사용자 인풋 자동으로 주입하기", "(if disabled, use ": "비활성화된 경우,", "macro for manual injection)": "매크로를 사용하여 수동으로 주입하세요.", "Only apply color as accent": "색상은 오직 강조로써만 적용됩니다", "qr--colorClear": "색상 지우기", "Color": "색상", "world_button_title": "캐릭터 로어. 클릭하여 로드하세요. Shift를 클릭하면 '월드 인포 링크' 팝업이 열립니다.", "Select TTS Provider": "TTS 공급자 선택", "tts_enabled": "활성화", "Narrate user messages": "사용자 메시지 나레이션", "Auto Generation": "자동 생성", "Requires auto generation to be enabled.": "자동 생성이 활성화 된 상태여야 합니다.", "Narrate by paragraphs (when streaming)": "(스트리밍이 진행 중일 때) 단락 별로 설명하기", "Only narrate quotes": "대사만 나레이션 하기", "Ignore text, even quotes, inside asterisk": "대사나 별표에 관계없이 모든 텍스트를 무시하기", "Narrate only the translated text": "오직 번역된 텍스트만 나레이션", "Skip codeblocks": "코드 블럭 안의 내용 읽지 않기", "Skip tagged blocks": "태그 안의 내용 읽지 않기", "Pass Asterisks to TTS Engine": "TTS 엔진에 별표 전달하기", "Audio Playback Speed": "음성 재생 속도", "Vectorization Model": "벡터화 모델", "Keep model in memory": "모델을 메모리에 유지하기", "Hint: Set the URL in the API connection settings.": "힌트: API 연결 설정에서 URL을 설정하세요.", "The server MUST be started with the --embedding flag to use this feature!": "이 기능을 사용하려면 서버를 --embedding 플래그와 함께 시작해야 합니다!", "Click to set": "설정하려면 클릭하세요", "Chunk boundary": "청크 바운더리", "World Info settings": "월드인포 설정", "Enable for World Info": "월드인포에 활성화", "Vectorization Source": "벡터화 근원", "File vectorization settings": "파일 벡터화 설정", "Enable for files": "파일에 활성화", "Chat vectorization settings": "채팅 벡터화 설정", "Enabled for chat messages": "채팅 메시지에 활성화", "Injection Position": "삽입 깊이", "Chunk size (chars)": "청크 사이즈 (단위: 문자당)", "Vector Summarization": "벡터 요약", "Summarize chat messages for vector generation": "벡터 생성을 위한 채팅 메시지 요약하기", "Warning: This will slow down vector generation drastically, as all messages have to be summarized first.": "경고: 모든 메시지를 먼저 요약해야 하므로 벡터 생성 속도가 크게 느려질 것입니다.", "Summarize chat messages when sending": "채팅 메시지를 전송할 때 요약하기", "Warning: This might cause your sent messages to take a bit to process and slow down response time.": "경고: 이로 인해 전송한 메시지가 처리되는 데 시간이 걸리고 응답 속도가 느려질 수 있습니다.", "Summarize with:": "요약을 위해 사용:", "Summary Prompt:": "요약 프롬프트", "Only used when Main API or WebLLM Extension is selected.": "메인 API 또는 WebLLM 확장이 선택될 때만 사용됩니다.", "Old messages are vectorized gradually as you chat. To process all previous messages, click the button below.": "이전 메시지는 채팅하는 동안 점진적으로 벡터화됩니다. 모든 이전 메시지를 처리하려면 아래 버튼을 클릭하세요.", "Vectorize All": "전부 벡터화", "Purge Vectors": "벡터 정리", "View Stats": "상태 확인", "Connection Profile": "연결 프로필", "Prompt Content": "프롬프트 내용", "Instruct Sequences": "지시 시퀀스", "Prefer Character Card Instructions": "캐릭터 카드의 지시사항을 선호", "If checked and the character card contains a Post-History Instructions override, use that instead": "활성화 된 경우, 캐릭터 카드에 Post-History 지시 무시 항목이 포함되어 있으면, 카드 지시사항의 내용으로 대신 사용합니다.", "Auto-select Input Text": "입력 텍스트 자동 선택", "Enable auto-select of input text in some text fields when clicking/selecting them. Applies to popup input textboxes, and possible other custom input fields.": "일부 텍스트 필드를 클릭하거나 선택할 때 자동으로 입력된 텍스트가 선택되도록 설정합니다. 팝업 입력창과 기타 커스텀 입력 필드에 적용됩니다.", "Markdown Hotkeys": "마크다운 입력 단축키", "markdown_hotkeys_desc": "특정 텍스트 입력창에서 마크다운 형식 문자를 입력하기 위한 단축키를 활성화합니다. '/help hotkeys'를 참고하세요.", "Show group chat queue": "그룹 채팅 대기열 표시", "In group chat, highlight the character(s) that are currently queued to generate responses and the order in which they will respond.": "그룹 채팅에서 응답을 생성하기 위해 현재 대기 중인 캐릭터와 응답할 순서를 강조 표시합니다.", "Quick 'Impersonate' button": "빠른 '사칭' 버튼", "Show a button in the input area to ask the AI to impersonate your character for a single message": "입력 영역에 AI에게 한 메시지 동안 당신의 캐릭터 연기를 사칭하도록 요청하는 버튼을 표시합니다.", "Injection Template": "삽입 템플릿", "Query messages": "쿼리 메시지 수", "Score threshold": "점수 임계값", "ext_sum_include_wi_scan": "월드 인포를 요약 범위에 포함", "(use _space": "이", "macro)": "매크로를 사용하세요", "Disable Send (Insert Into Input Field)": "전송 비활성화 (입력 필드에 삽입)", "Place Quick Reply Before Input": "빠른 답변을 입력란 앞에 배치", "Chat Message Visibility (by source)": "채팅 메시지 가시성 (근원별)", "Uncheck to hide the extension's messages in chat prompts.": "체크를 해제하여 채팅 프롬프트에서 확장 프로그램의 메시지를 숨기세요.", "Extensions Menu": "확장 메뉴", "Slash Command": "슬래시 명령어", "Interactive Mode": "인터랙티브 모드", "Load a custom asset list or select": "커스텀 에셋 목록을 불러오거나", "to install 3rd party extensions.": "를 선택하여 3rd 파티 확장 프로그램을 설치하세요.", "Assets URL": "에셋 URL", "Load an asset list": "에셋 목록을 불러옵니다.", "ext_translate_btn_chat": "채팅 번역하기", "ext_translate_btn_input": "입력 번역하기", "Ask": "묻기", "tag_import_none": "불러오지 않음", "tag_import_all": "전부", "tag_import_existing": "기존 태그 참조", "You can add more": "원한다면", "or_welcome": "또는", "from other websites": "를 통해 다른 웹사이트들로부터 불러올 수 있습니다.", "Go to the": "추가 기능 설치를 하려면", "to install additional features.": "으로 가세요.", "Master Import": "마스터 불러오기", "Master Export": "마스터 내보내기", "Chat Quick Reply Sets": "채팅 빠른 답장 세트들"}