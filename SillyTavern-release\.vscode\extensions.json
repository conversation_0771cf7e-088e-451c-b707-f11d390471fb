{
    // See https://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
    // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
    // List of extensions which should be recommended for users of this workspace.
    "recommendations": [
        "dbaeumer.vscode-eslint",
        "EditorConfig.EditorConfig",
        "mrcrowl.easy-less"
    ],
    // List of extensions recommended by VS Code that should not be recommended for users of this workspace.
    "unwantedRecommendations": []
}
