---
type: "always_apply"
description: "Backend development workflow with Java/Python/Node.js - 后端开发完整工作流"
---
# ⚙️ Backend Development Workflow - 后端开发工作流

专为 Java、Python、Node.js 等后端技术栈优化的完整开发工作流程。

## 🚀 Commands - 命令

- `/backend-dev` - 启动后端开发工作流
- `/api-design` - API接口设计
- `/database-design` - 数据库设计
- `/security-check` - 安全检查
- `/performance-test` - 性能测试

### 专用配置模式
- `/设置微服务模式` - 启用微服务架构优先策略
- `/设置单体模式` - 启用单体应用架构策略
- `/设置性能优化模式` - 自动应用性能优化建议

### 反馈频率控制
- `/设置详细模式` - 启用所有反馈点，完整工作流
- `/设置标准模式` - 关键决策点反馈（默认）
- `/设置静默模式` - 仅错误时反馈，适合熟练用户

### 工作流配置
- `/设置严格模式` - 严格按顺序执行，不允许跳过
- `/设置灵活模式` - 允许模式跳转和流程调整（默认）
- `/设置快捷模式` - 简化某些步骤，提高效率

### 质量标准配置
- `/设置企业级标准` - 最高质量要求，完整测试
- `/设置标准级别` - 平衡质量和效率（默认）
- `/设置原型级别` - 快速验证，降低质量要求

## 📋 Process - 开发流程

### 1. 🔍 需求分析 (Requirements Analysis)
- 分析业务需求和技术约束
- 确定 API 规范和数据模型
- 评估性能和扩展性要求
- **转换条件**：需求明确，技术方案可行

### 2. 🏗️ 架构设计 (Architecture Design)
- 设计系统架构和模块划分
- 选择技术栈和中间件
- 规划数据库设计和缓存策略
- **转换条件**：架构设计完成，技术选型确定

### 3. ⚡ API开发 (API Development)
- 实现 RESTful API 或 GraphQL 接口
- 集成认证授权机制
- 实现业务逻辑和数据处理
- **转换条件**：API功能完成，接口测试通过

### 4. 🗄️ 数据建模 (Data Modeling)
- 设计数据库表结构和关系
- 实现数据访问层和 ORM
- 优化查询性能和索引
- **转换条件**：数据模型稳定，性能达标

### 5. 🧪 测试验证 (Testing)
- 单元测试和集成测试
- API 接口测试和压力测试
- 安全测试和漏洞扫描
- **转换条件**：测试通过，质量达标

### 6. 🚀 部署运维 (Deploy & Operations)
- 容器化和 CI/CD 配置
- 监控和日志系统
- 性能调优和故障处理
- **转换条件**：部署成功，运行稳定

## 🛠️ Technology Stack - 技术栈

### Languages & Frameworks - 语言和框架
- **Java** - Spring Boot, Spring Security, JPA
- **Python** - FastAPI, Django, SQLAlchemy
- **Node.js** - Express, Koa, NestJS
- **Go** - Gin, Echo, GORM
- **Rust** - Actix-web, Rocket, Diesel

### Databases - 数据库
- **关系型** - PostgreSQL, MySQL
- **NoSQL** - MongoDB, Redis
- **搜索** - Elasticsearch
- **消息队列** - RabbitMQ, Kafka

### DevOps & Tools - 运维工具
- **容器化** - Docker, Kubernetes
- **CI/CD** - GitHub Actions, Jenkins
- **监控** - Prometheus, Grafana
- **日志** - ELK Stack

## ✅ Best Practices - 最佳实践

### API Design - API设计
- RESTful 设计原则
- 统一的响应格式
- 完善的错误处理
- API 版本管理

### Security - 安全
- 输入验证和参数化查询
- 认证和授权机制
- HTTPS 和数据加密
- 安全头和 CORS 配置

### Performance - 性能
- 数据库查询优化
- 缓存策略实施
- 异步处理
- 连接池配置

### Code Quality - 代码质量
- 分层架构设计
- 单元测试覆盖
- 代码规范遵循
- 文档完整性

## 🔧 Development Commands - 开发命令

### Java (Spring Boot)
```bash
# 项目创建
spring init --dependencies=web,jpa,security my-project

# 运行
./mvnw spring-boot:run

# 测试
./mvnw test

# 构建
./mvnw clean package
```

### Python (FastAPI)
```bash
# 项目创建
pip install fastapi uvicorn

# 运行
uvicorn main:app --reload

# 测试
pytest

# 依赖管理
pip freeze > requirements.txt
```

### Node.js (Express)
```bash
# 项目创建
npm init -y
npm install express

# 运行
npm start

# 测试
npm test

# 构建
npm run build
```

## 📋 Checklist - 检查清单

### 架构设计
- [ ] 系统架构图完成
- [ ] 技术栈选择合理
- [ ] 数据库设计优化
- [ ] 安全方案确定

### API开发
- [ ] 接口设计规范
- [ ] 认证授权实现
- [ ] 错误处理完善
- [ ] 接口文档完整

### 数据层
- [ ] 数据模型设计
- [ ] 查询性能优化
- [ ] 数据迁移脚本
- [ ] 备份策略制定

### 测试部署
- [ ] 单元测试覆盖
- [ ] 集成测试通过
- [ ] 性能测试达标
- [ ] 部署流程验证

## 🎯 Mode Switching - 模式切换

根据当前任务自动或手动切换到对应模式：

- **架构设计模式** - `/architecture`
- **API开发模式** - `/api-dev`
- **数据建模模式** - `/data-model`
- **安全开发模式** - `/security`
- **运维部署模式** - `/devops`

### 专用配置模式行为
- **微服务模式** - 激活后优先推荐分布式架构、API网关、服务发现
- **单体模式** - 激活后推荐传统分层架构、简化部署方案
- **性能优化模式** - 激活后自动建议缓存、数据库优化、异步处理
