# Design Document

## Overview

AI小说IDE (NovelCraft AI Studio) 是一个基于现代Web技术栈构建的桌面应用程序，采用Electron + React + Node.js的混合架构。系统集成了多项先进技术：SillyTavern酒馆系统、Graphiti知识图谱、2.txt工具的成熟算法、以及AI工作流引擎，为小说创作者提供全方位的智能创作支持。

系统采用微服务架构，通过Docker Compose部署，支持PostgreSQL + Neo4j + Redis的多数据库架构，确保高性能和可扩展性。

## Architecture

### 系统整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI小说IDE系统架构                              │
├─────────────────────────────────────────────────────────────────┤
│  前端层 (Electron + React + TypeScript)                        │
│  ┌─────────────┬─────────────────┬─────────────────────────────┐ │
│  │ 左侧导航栏   │   中央编辑器     │      右侧AI聊天面板          │ │
│  │ - 文件管理   │ - Monaco Editor │ - 酒馆控制面板              │ │
│  │ - 大纲视图   │ - AI助手集成    │ - 对话历史                  │ │
│  │ - 知识库     │ - 实时协作      │ - 快速操作                  │ │
│  │ - 设置面板   │ - 语法高亮      │ - 输入区域                  │ │
│  └─────────────┴─────────────────┴─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  API网关层 (Express + TypeScript)                               │
│  ┌─────────────┬─────────────────┬─────────────────────────────┐ │
│  │ 认证中间件   │   路由管理       │      限流控制               │ │
│  │ - JWT验证   │ - RESTful API   │ - 请求频率限制              │ │
│  │ - 权限控制   │ - WebSocket     │ - 负载均衡                  │ │
│  └─────────────┴─────────────────┴─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Node.js + TypeScript)                              │
│  ┌─────────────┬─────────────────┬─────────────────────────────┐ │
│  │ 酒馆系统     │   知识图谱       │      AI工作流引擎           │ │
│  │ - 角色管理   │ - Graphiti集成  │ - 可视化设计器              │ │
│  │ - 世界书     │ - Neo4j图数据库 │ - 节点执行器                │ │
│  │ - 预设管理   │ - 语义搜索      │ - 工作流模板                │ │
│  └─────────────┴─────────────────┴─────────────────────────────┘ │
│  ┌─────────────┬─────────────────┬─────────────────────────────┐ │
│  │ 改编引擎     │   批量处理       │      文件管理               │ │
│  │ - 雷点识别   │ - 多章节处理    │ - 项目管理                  │ │
│  │ - 情节重构   │ - PSKB系统      │ - 版本控制                  │ │
│  │ - 质量控制   │ - 并行处理      │ - 备份恢复                  │ │
│  └─────────────┴─────────────────┴─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  数据存储层                                                      │
│  ┌─────────────┬─────────────────┬─────────────────────────────┐ │
│  │ PostgreSQL  │    Neo4j        │      Redis                  │ │
│  │ - 主数据库   │ - 知识图谱      │ - 缓存系统                  │ │
│  │ - pgvector  │ - 关系数据      │ - 会话存储                  │ │
│  │ - 向量搜索   │ - 图算法        │ - 任务队列                  │ │
│  └─────────────┴─────────────────┴─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```### 技
术栈选择

#### 前端技术栈
- **React 19**: 最新版本的React，支持并发特性和服务器组件
- **TypeScript**: 提供类型安全，提高代码质量和开发效率
- **Vite 6**: 现代化构建工具，快速的热重载和优化的生产构建
- **Monaco Editor**: VS Code同款编辑器，功能强大，扩展性好
- **Tailwind CSS**: 原子化CSS框架，快速构建现代UI
- **Zustand**: 轻量级状态管理库，简单易用

#### 后端技术栈
- **Electron**: 跨平台桌面应用框架，基于Chromium和Node.js
- **Node.js**: JavaScript运行时，与前端技术栈统一
- **Express**: 轻量级Web框架，中间件生态丰富
- **TypeScript**: 后端也使用TypeScript确保类型安全
- **Prisma**: 现代ORM，类型安全，数据库迁移便捷
- **PostgreSQL**: 企业级关系数据库，支持JSON和向量扩展
- **Redis**: 高性能缓存数据库，支持多种数据结构

#### AI和图数据库
- **Graphiti**: 先进的知识图谱框架，支持时间感知
- **Neo4j**: 领先的图数据库，查询语言Cypher强大
- **pgvector**: PostgreSQL向量扩展，支持相似度搜索

### Graphiti知识图谱集成架构

#### MCP协议集成方案
```typescript
// MCP客户端接口定义
interface GraphitiMCPClient {
  // 基础图谱操作
  addEpisode(episode: EpisodeData): Promise<GraphResult>
  searchFacts(query: string, filters?: SearchFilters): Promise<Fact[]>
  searchNodes(query: string, filters?: SearchFilters): Promise<Node[]>
  
  // 小说专用工具
  analyzeCharacterArc(character: string, projectId: string): Promise<CharacterAnalysis>
  detectPlotHoles(chapterRange: [number, number], projectId: string): Promise<PlotHole[]>
  suggestPlotDevelopment(context: string, projectId: string): Promise<PlotSuggestion[]>
  checkTimelineConsistency(projectId: string): Promise<ConsistencyReport>
}

// 统一数据处理管道
class UnifiedDataPipeline {
  async processChapter(chapterData: ChapterData): Promise<ProcessedChapter> {
    // 1. 2.txt传统处理
    const enhanced = await this.pskbProcessor.enhance(chapterData)
    
    // 2. Graphiti图谱更新
    const graphResult = await this.graphitiClient.addEpisode({
      name: `Chapter_${chapterData.id}`,
      content: enhanced.content,
      projectId: chapterData.projectId
    })
    
    // 3. 智能分析
    const insights = await this.graphitiClient.searchFacts(
      "角色发展 情节冲突 伏笔线索"
    )
    
    return {
      enhancedContent: enhanced.content,
      graphEntities: graphResult.entities,
      plotInsights: insights,
      consistencyCheck: await this.checkConsistency(insights)
    }
  }
}
```## Com
ponents and Interfaces

### 前端组件架构

#### 核心布局组件
```typescript
// 主应用组件
interface AppLayoutProps {
  theme: 'light' | 'dark'
  layout: LayoutConfig
}

// 左侧导航栏
interface SidebarProps {
  activeTab: 'files' | 'outline' | 'knowledge' | 'settings'
  projects: Project[]
  onTabChange: (tab: string) => void
}

// 中央编辑器
interface EditorProps {
  content: string
  language: string
  aiSuggestions: AISuggestion[]
  onContentChange: (content: string) => void
  onSelectionChange: (selection: Selection) => void
}

// 右侧AI聊天面板
interface ChatPanelProps {
  character: Character
  worldBooks: WorldBook[]
  preset: AIPreset
  messages: ChatMessage[]
  onSendMessage: (message: string) => void
}
```

#### SillyTavern酒馆系统组件
```typescript
// 角色管理组件
interface CharacterManagerProps {
  characters: Character[]
  activeCharacter: Character | null
  onCharacterSelect: (character: Character) => void
  onCharacterImport: (file: File) => void
}

// 世界书管理组件
interface WorldBookManagerProps {
  worldBooks: WorldBook[]
  activeEntries: WorldBookEntry[]
  onEntryActivate: (entry: WorldBookEntry) => void
  onEntryEdit: (entry: WorldBookEntry) => void
}

// 预设管理组件
interface PresetManagerProps {
  presets: AIPreset[]
  activePreset: AIPreset
  onPresetChange: (preset: AIPreset) => void
  onParameterAdjust: (params: AIParameters) => void
}
```

#### 知识图谱可视化组件
```typescript
// 图谱浏览器组件
interface GraphBrowserProps {
  nodes: GraphNode[]
  edges: GraphEdge[]
  selectedNode: GraphNode | null
  onNodeSelect: (node: GraphNode) => void
  onNodeExpand: (node: GraphNode) => void
}

// 智能分析面板
interface AnalysisPanelProps {
  analysisResults: AnalysisResult[]
  plotHoles: PlotHole[]
  characterArcs: CharacterArc[]
  onAnalysisRequest: (type: AnalysisType) => void
}
```

### 后端服务架构

#### API路由设计
```typescript
// 项目管理API
router.get('/api/projects', ProjectController.getProjects)
router.post('/api/projects', ProjectController.createProject)
router.get('/api/projects/:id', ProjectController.getProject)
router.put('/api/projects/:id', ProjectController.updateProject)
router.delete('/api/projects/:id', ProjectController.deleteProject)

// 章节管理API
router.get('/api/projects/:id/chapters', ChapterController.getChapters)
router.post('/api/projects/:id/chapters', ChapterController.createChapter)
router.get('/api/chapters/:id', ChapterController.getChapter)
router.put('/api/chapters/:id', ChapterController.updateChapter)
router.delete('/api/chapters/:id', ChapterController.deleteChapter)

// 批量处理API
router.post('/api/batch/process', BatchController.startBatchProcessing)
router.get('/api/batch/jobs/:id', BatchController.getJobStatus)
router.post('/api/batch/jobs/:id/cancel', BatchController.cancelJob)
router.get('/api/batch/jobs/:id/logs', BatchController.getJobLogs)

// 酒馆系统API
router.get('/api/tavern/characters', TavernController.getCharacters)
router.post('/api/tavern/characters', TavernController.createCharacter)
router.post('/api/tavern/chat', TavernController.sendMessage)
router.get('/api/tavern/worldbooks', TavernController.getWorldBooks)

// 知识图谱API
router.post('/api/graph/episodes', GraphController.addEpisode)
router.get('/api/graph/search', GraphController.searchGraph)
router.get('/api/graph/analysis/:type', GraphController.getAnalysis)
```

#### 核心服务类设计
```typescript
// 2.txt批量处理服务
class ChapterBatchProcessor {
  private processingQueue: Queue<ProcessingTask>
  private apiKeyManager: APIKeyManager
  private pskbManager: PSKBManager
  
  async startBatchProcessing(
    chapters: Chapter[],
    mode: ProcessingMode,
    settings: ProcessingSettings
  ): Promise<BatchJob>
  
  private async processStrictSerial(job: BatchJob): Promise<void>
  private async processParallel(job: BatchJob): Promise<void>
  private async processPureEnhancement(job: BatchJob): Promise<void>
}

// PSKB知识库管理服务
class PSKBManager {
  async generatePSKB(chapters: Chapter[]): Promise<PSKB>
  async updatePSKB(pskb: PSKB, updates: PSKBUpdate[]): Promise<PSKB>
  async getPSKBForChapter(chapterIndex: number): Promise<PSKB>
  private async chunkAndAnalyze(content: string): Promise<ChunkAnalysis[]>
}

// WKB/EKB分析服务
class WKBAnalysisService {
  async analyzeStyleGuide(content: string): Promise<StyleGuide>
  async generateMasterOutline(summaries: PlotSummary[]): Promise<MasterOutline>
  async extractEventKnowledge(content: string): Promise<EventKnowledge[]>
  private async runParallelAnalysis(chunks: TextChunk[]): Promise<AnalysisResult[]>
}
```## 
Data Models

### 核心数据模型

#### 项目和章节模型
```typescript
interface Project {
  id: string
  name: string
  description?: string
  settings: ProjectSettings
  createdAt: Date
  updatedAt: Date
}

interface Chapter {
  id: string
  projectId: string
  title: string
  content: string
  orderIndex: number
  wordCount: number
  status: 'draft' | 'processing' | 'completed'
  metadata: ChapterMetadata
  createdAt: Date
  updatedAt: Date
}

interface ProjectSettings {
  aiModel: string
  processingMode: 'strict_serial' | 'parallel' | 'pure_enhancement'
  concurrencyLimit: number
  promptTemplate: string
  enableGraphiti: boolean
}
```

#### PSKB知识库模型
```typescript
interface PSKB {
  id: string
  projectId: string
  version: number
  strategicPlan: StrategyPlan
  characterProfiles: CharacterProfile[]
  plotThreads: PlotThread[]
  worldSettings: WorldSetting[]
  createdAt: Date
  updatedAt: Date
}

interface StrategyPlan {
  mainObjective: string
  keyMilestones: Milestone[]
  characterDevelopment: CharacterDevelopment[]
  plotProgression: PlotProgression[]
}

interface CharacterProfile {
  name: string
  role: string
  personality: string
  relationships: Relationship[]
  developmentArc: DevelopmentArc
}
```

#### SillyTavern酒馆模型
```typescript
interface Character {
  id: string
  name: string
  description: string
  personality: string
  scenario: string
  firstMessage: string
  exampleDialogue: string
  avatar?: string
  createdAt: Date
}

interface WorldBook {
  id: string
  name: string
  description?: string
  entries: WorldBookEntry[]
  createdAt: Date
}

interface WorldBookEntry {
  uid: number
  key: string[]
  keysecondary?: string[]
  content: string
  constant: boolean
  selective: boolean
  insertionOrder: number
  enabled: boolean
  position: 'before_char' | 'after_char'
  extensions: Record<string, any>
}

interface AIPreset {
  id: string
  name: string
  temperature: number
  topP: number
  topK: number
  repetitionPenalty: number
  maxTokens: number
  stopSequences: string[]
}
```

#### Graphiti知识图谱模型
```typescript
interface GraphNode {
  id: string
  type: 'Character' | 'Location' | 'Event' | 'PlotPoint'
  name: string
  properties: Record<string, any>
  timestamp: Date
  projectId: string
}

interface GraphEdge {
  id: string
  sourceId: string
  targetId: string
  type: string
  properties: Record<string, any>
  strength: number
  timestamp: Date
}

interface EpisodeData {
  name: string
  content: string
  projectId: string
  chapterId?: string
  timestamp?: Date
}

interface AnalysisResult {
  type: 'character_arc' | 'plot_holes' | 'timeline_consistency'
  findings: Finding[]
  suggestions: Suggestion[]
  confidence: number
}
```

### 数据库设计

#### PostgreSQL表结构
```sql
-- 项目管理表
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 章节管理表
CREATE TABLE chapters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    order_index INTEGER NOT NULL,
    word_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'draft',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- PSKB知识库表
CREATE TABLE project_story_knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    version INTEGER DEFAULT 1,
    strategic_plan JSONB,
    character_profiles JSONB DEFAULT '[]',
    plot_threads JSONB DEFAULT '[]',
    world_settings JSONB DEFAULT '[]',
    embedding vector(1536),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 角色管理表
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    personality TEXT,
    scenario TEXT,
    first_message TEXT,
    example_dialogue TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 批量处理任务表
CREATE TABLE batch_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    total_items INTEGER DEFAULT 0,
    settings JSONB DEFAULT '{}',
    results JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```## Error 
Handling

### 错误处理策略

#### 前端错误处理
```typescript
// 全局错误边界
class GlobalErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误到日志系统
    logger.error('Frontend Error:', error, errorInfo)
    
    // 发送错误报告到后端
    errorReporter.report(error, errorInfo)
    
    // 显示用户友好的错误信息
    this.setState({ hasError: true, error })
  }
}

// API调用错误处理
class APIClient {
  async request<T>(config: RequestConfig): Promise<T> {
    try {
      const response = await fetch(config.url, config)
      
      if (!response.ok) {
        throw new APIError(response.status, await response.text())
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof APIError) {
        throw error
      }
      
      // 网络错误或其他未知错误
      throw new NetworkError('网络连接失败，请检查网络设置')
    }
  }
}
```

#### 后端错误处理
```typescript
// 全局错误处理中间件
const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Backend Error:', error)
  
  if (error instanceof ValidationError) {
    return res.status(400).json({
      error: 'VALIDATION_ERROR',
      message: error.message,
      details: error.details
    })
  }
  
  if (error instanceof DatabaseError) {
    return res.status(500).json({
      error: 'DATABASE_ERROR',
      message: '数据库操作失败，请稍后重试'
    })
  }
  
  if (error instanceof AIServiceError) {
    return res.status(503).json({
      error: 'AI_SERVICE_ERROR',
      message: 'AI服务暂时不可用，请稍后重试'
    })
  }
  
  // 未知错误
  res.status(500).json({
    error: 'INTERNAL_ERROR',
    message: '服务器内部错误'
  })
}

// AI服务重试机制
class AIServiceClient {
  async callAI(prompt: string, options: AIOptions): Promise<string> {
    const maxRetries = 3
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.makeRequest(prompt, options)
      } catch (error) {
        lastError = error
        
        if (error instanceof RateLimitError) {
          // 指数退避
          const delay = Math.pow(2, attempt) * 1000
          await this.sleep(delay)
          continue
        }
        
        if (error instanceof SafetyError) {
          // 尝试使用安全检测绕过
          options = await this.applySafetyBypass(options)
          continue
        }
        
        // 其他错误直接抛出
        throw error
      }
    }
    
    throw lastError
  }
}
```

### 数据验证和安全

#### 输入验证
```typescript
// 使用Zod进行数据验证
const ProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  settings: z.object({
    aiModel: z.string(),
    processingMode: z.enum(['strict_serial', 'parallel', 'pure_enhancement']),
    concurrencyLimit: z.number().min(1).max(10)
  })
})

const ChapterSchema = z.object({
  title: z.string().min(1).max(255),
  content: z.string(),
  orderIndex: z.number().min(0)
})

// API路由中的验证
router.post('/api/projects', async (req, res, next) => {
  try {
    const validatedData = ProjectSchema.parse(req.body)
    const project = await projectService.create(validatedData)
    res.json(project)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'VALIDATION_ERROR',
        details: error.errors
      })
    }
    next(error)
  }
})
```

#### 安全措施
```typescript
// JWT认证中间件
const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]
  
  if (!token) {
    return res.status(401).json({ error: 'ACCESS_TOKEN_REQUIRED' })
  }
  
  jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'INVALID_TOKEN' })
    }
    req.user = user
    next()
  })
}

// 数据加密
class EncryptionService {
  private readonly algorithm = 'aes-256-gcm'
  private readonly key = Buffer.from(process.env.ENCRYPTION_KEY!, 'hex')
  
  encrypt(text: string): EncryptedData {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(this.algorithm, this.key)
    cipher.setAAD(Buffer.from('additional-data'))
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const authTag = cipher.getAuthTag()
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    }
  }
}
```

## Testing Strategy

### 测试架构

#### 前端测试
```typescript
// 组件单元测试 (Jest + React Testing Library)
describe('EditorComponent', () => {
  test('should handle content changes', () => {
    const onContentChange = jest.fn()
    render(<Editor content="test" onContentChange={onContentChange} />)
    
    const editor = screen.getByRole('textbox')
    fireEvent.change(editor, { target: { value: 'new content' } })
    
    expect(onContentChange).toHaveBeenCalledWith('new content')
  })
  
  test('should display AI suggestions', () => {
    const suggestions = [{ id: '1', content: 'AI suggestion' }]
    render(<Editor content="test" aiSuggestions={suggestions} />)
    
    expect(screen.getByText('AI suggestion')).toBeInTheDocument()
  })
})

// 集成测试 (Cypress)
describe('AI Novel IDE E2E', () => {
  it('should create and edit a project', () => {
    cy.visit('/')
    cy.get('[data-testid=create-project]').click()
    cy.get('[data-testid=project-name]').type('Test Novel')
    cy.get('[data-testid=submit]').click()
    
    cy.get('[data-testid=editor]').type('Chapter 1 content')
    cy.get('[data-testid=save]').click()
    
    cy.contains('保存成功').should('be.visible')
  })
})
```

#### 后端测试
```typescript
// API单元测试 (Jest + Supertest)
describe('Projects API', () => {
  test('POST /api/projects should create project', async () => {
    const projectData = {
      name: 'Test Project',
      description: 'Test Description'
    }
    
    const response = await request(app)
      .post('/api/projects')
      .send(projectData)
      .expect(201)
    
    expect(response.body.name).toBe(projectData.name)
    expect(response.body.id).toBeDefined()
  })
  
  test('GET /api/projects should return projects list', async () => {
    const response = await request(app)
      .get('/api/projects')
      .expect(200)
    
    expect(Array.isArray(response.body)).toBe(true)
  })
})

// 服务层测试
describe('ChapterBatchProcessor', () => {
  test('should process chapters in strict serial mode', async () => {
    const processor = new ChapterBatchProcessor()
    const chapters = [
      { id: '1', content: 'Chapter 1' },
      { id: '2', content: 'Chapter 2' }
    ]
    
    const job = await processor.startBatchProcessing(
      chapters,
      'strict_serial',
      { concurrency: 1 }
    )
    
    expect(job.status).toBe('pending')
    expect(job.totalItems).toBe(2)
  })
})
```

#### 性能测试
```typescript
// 负载测试配置
const loadTestConfig = {
  scenarios: {
    api_load_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: '2m', target: 10 },
        { duration: '5m', target: 50 },
        { duration: '2m', target: 0 }
      ]
    }
  },
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求在500ms内完成
    http_req_failed: ['rate<0.1']     // 错误率低于10%
  }
}

// 数据库性能测试
describe('Database Performance', () => {
  test('vector search should complete within 1 second', async () => {
    const startTime = Date.now()
    
    const results = await vectorSearch.search('test query', {
      limit: 10,
      threshold: 0.8
    })
    
    const duration = Date.now() - startTime
    expect(duration).toBeLessThan(1000)
    expect(results.length).toBeGreaterThan(0)
  })
})
```