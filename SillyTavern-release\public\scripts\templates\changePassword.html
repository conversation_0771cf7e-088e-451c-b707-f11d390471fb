<form action="javascript:void(0);" class="flex-container flexFlowColumn">
    <div class="currentPasswordBlock">
        <label data-i18n="Current Password:" for="user">Current Password:</label>
        <input type="password" name="current" class="text_pole" placeholder="[ No password ]" autocomplete="current-password">
    </div>
    <div class="newPasswordBlock">
        <label data-i18n="New Password:" for="password">New Password:</label>
        <input type="password" name="password" class="text_pole" placeholder="[ No password ]" autocomplete="new-password">
    </div>
    <div class="confirmPasswordBlock">
        <label data-i18n="Confirm New Password:" for="confirm">Confirm New Password:</label>
        <input type="password" name="confirm" class="text_pole" placeholder="[ No password ]" autocomplete="new-password">
    </div>
</form>
