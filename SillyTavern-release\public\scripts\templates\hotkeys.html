<div>
    <strong data-i18n="help_hotkeys_0">Chat Hotkeys</strong>
</div>
<ul>
    <li><kbd data-i18n="help_hotkeys_1">Up</kbd> = <span data-i18n="help_hotkeys_2">Edit last message in chat</span></li>
    <li><kbd data-i18n="help_hotkeys_3">Ctrl+Up</kbd> = <span data-i18n="help_hotkeys_4">Edit last USER message in chat</span></li>
    <li><kbd data-i18n="help_hotkeys_5">Left</kbd> = <span data-i18n="help_hotkeys_6">swipe left</span></li>
    <li><kbd data-i18n="help_hotkeys_7">Right</kbd> = <span data-i18n="help_hotkeys_8">swipe right (NOTE: swipe hotkeys only apply without modifiers and are disabled when chatbar has something typed into it)</span></li>
    <li><kbd data-i18n="help_hotkeys_9">Enter</kbd> <span data-i18n="help_hotkeys_10">(with chat bar selected)</span> = <span data-i18n="help_hotkeys_10_1">send your message to AI</span></li>
    <li><kbd data-i18n="help_hotkeys_11">Ctrl+Enter</kbd> = <span data-i18n="help_hotkeys_12">Regenerate the last AI response</span></li>
    <li><kbd data-i18n="help_hotkeys_13">Alt+Enter</kbd> = <span data-i18n="help_hotkeys_14">Continue the last AI response</span></li>
    <li><kbd data-i18n="help_hotkeys_15">Escape</kbd> = <span data-i18n="help_hotkeys_16">stop AI response generation, close UI panels, cancel message edit</span></li>
    <li><kbd data-i18n="help_hotkeys_17">Ctrl+Shift+Up</kbd> = <span data-i18n="help_hotkeys_18">Scroll to context line</span></li>
    <li><kbd data-i18n="help_hotkeys_19">Ctrl+Shift+Down</kbd> = <span data-i18n="help_hotkeys_20">Scroll chat to bottom</span></li>
</ul>
<div>
    <strong data-i18n="help_hotkeys_20">Markdown Hotkeys</strong>
</div>
<div>
    <small>
        <span data-i18n="help_hotkeys_21">Works in the chatbar and textareas marked with this icon:</span>
        <code><i class="fa-brands fa-markdown"></i></code>
    </small>
</div>
<ul>
    <li><kbd>Ctrl+B</kbd> = <span data-i18n="help_hotkeys_22">**bold**</span></li>
    <li><kbd>Ctrl+I</kbd> = <span data-i18n="help_hotkeys_23">*italic*</span></li>
    <li><kbd>Ctrl+U</kbd> = <span data-i18n="help_hotkeys_24">__underline__</span></li>
    <li><kbd>Ctrl+K</kbd> = <span data-i18n="help_hotkeys_25">`inline code`</span></li>
    <li><kbd>Ctrl+Shift+~</kbd> = <span data-i18n="help_hotkeys_26">~~strikethrough~~</span></li>
</ul>
