#preloader {
    position: fixed;
    margin: 0;
    padding: 0;
    top: 0;
    left: 0;
    z-index: 999999;
    width: 100vw;
    height: 100vh;
    width: 100dvw;
    height: 100dvh;
    background-color: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    /*for some reason the full screen blur does not work on iOS*/
    backdrop-filter: blur(30px);
    opacity: 1;
}

#load-spinner {
    --spinner-size: 2em;
    transition: all var(--animation-duration-2x) ease-out;
    opacity: 1;
    top: calc(50% - var(--spinner-size) / 2);
    left: calc(50% - var(--spinner-size) / 2);
    position: absolute;
    width: var(--spinner-size);
    height: var(--spinner-size);
    display: flex;
    align-items: center;
    justify-content: center;
}
