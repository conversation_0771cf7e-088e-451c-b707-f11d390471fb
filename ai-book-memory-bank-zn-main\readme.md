#重要
搭配claude code 使用

# 书籍记忆库

一种结构化的文档系统，用于在书籍写作过程中维护上下文和一致性。此记忆库通过提供一个持续更新的知识库，帮助作者与 LLM 助手合作，使无状态 AI 能够在写作会话之间保持完整的上下文。

书籍记忆库旨在解决使用 AI 辅助写作书籍时的关键挑战：保持一致的上下文。它包括：

1. **核心文档文件**，用于故事情节元素

## 概览
2. **专门模板**用于角色、剧情和世界观构建
3. **自动化脚本**用于手稿准备
4. **工作流指南**用于有效的人工智能协作
5. **自定义指令**用于 LLM 助手
6. **对话记忆更新**，消除脚本的需要

## 开始使用

1. **设置项目结构**：
   - 将 book-memory-bank 文件夹复制到项目目录
- 在根目录下创建 .clinerules 文件夹，并将 custom_instructions.md 的内容复制进去
   - 在 .clinerules 文件夹内，将此目录下的 memory-bank.md 复制到该目录中
   - 在 .clinerules 文件夹内，将此目录下的 author-rules.md 复制到该目录中

2. **配置 AI 辅助程序**
- 将 custom_instructions.md 的内容复制到自定义指令中
   - 确保自定义指令中的文件路径包含 "book-memory-bank/" 前缀
   - 在主项目目录中打开 VSCode

3. **启动项目**：
- 以计划模式开始，建立基本的故事元素  
- 描述你想写的内容。让第一行有意义，因为这将是 Clines 历史记录中显示的内容。  
- 添加细节 - 你可以从基本描述开始，然后迭代，或者添加你想要的任何详细信息。这可能包括主要角色、第一人称或第三人称视角、语气、情节要点、地点等。建议设定书籍的整体风格。例如，“以欧内斯特·海明威的风格写这本书” - 或者“以简约直接的风格写这本书，使用简单的语言、短句，并通过行动和对话含蓄地传达深层意义”（这在某种程度上类似于海明威的写作风格）
- 回答任何给你提出的问题
- 在提示中输入“initialize memory bank”。这将读取内存银行

4. **写作过程**：
   - 一旦你准备好开始写作，就进入 Act 模式进行写作。
   - 它可能会写一些内容。
- 让克莱恩执行“更新记忆库”操作。这应该会完全创建记忆库文件，包括角色、地点、剧情等。
- 关于要撰写的总纲。进行审阅并反馈修改意见（如果需要）
- 等到总纲达到你想要的样子后，再执行“更新记忆库”操作。这样可以确保你从最新的记忆库开始
- 你现在可以反复调整，目前看来最有效的模式是：
1.  请求撰写“第一章大纲”（或仅请求“撰写下一章大纲”）。
      2.  审阅并反馈修改意见
      3.  请求撰写“第一章”（或仅请求“撰写下一章”）。
      4.  审阅并反馈修改意见
5. 更新记忆库
6. 重复上述步骤直至书籍完成
- 建议在每章撰写完成后阅读并反馈——稍后可以通过提示进行大幅修改，但这会花费较长时间且可能出错
- 定期回顾所有记忆库文件——如发现问题，请输入提示进行修正

5. **发布准备**：
   - 在 Production/Scripts 目录中运行自动化脚本以组装和格式化您的手稿   

## 完整项目结构

推荐的项目结构将记忆库（元数据和上下文）与实际书籍内容分开：

YourBookProject/              # 项目根目录
├── .clinerules                # Cline 规则
├── └── book-memory-bank.md    # 用于书籍记忆的 Cline 规则
├── └── author-rules.md        # 类似人类作者行为的 Cline 规则
├── Chapters/                  # 实际章节文件
│   └── Chapter01.md
│   └── Chapter02.md
├── Outlines/                  # 实际大纲
│   ├── Master_Outline.md
│   └── Chapter_Outlines/
│       └── Chapter01_Outline.md
|── AI 生成/             # 实际的 AI 生成
│   ├── cover_prompts.md       # 实际封面提示词   
```
├── Manuscript/                # 生成的输出文件
└── book-memory-bank/          # 内存库文件（仅包含上下文）
    ├── README.md              # 内存库文档（此文件）
|── system_prompt.md       # 建议的系统提示 - 在克莱恩中，前往设置并将该文件的内容粘贴到“自定义提示”中
    ├── custom_instructions.md  # AI 助手指令     
    │
    ├── Core/                   # 核心故事文档
│   ├── projectbrief.md     # 核心项目定义
    │   ├── story_structure.md  # 故事目的、定位和叙事模式
    │   ├── world_and_characters.md # 世界构建元素和角色档案
    │   ├── activeContext.md    # 当前工作状态
│   └── progress.md         # 完成度跟踪
    │
    ├── Core/Templates/         # 新内容的模板文件
    │   ├── README.md           # 模板使用指南
│   ├── master_outline_template.md  # 故事结构模板
    │   └── chapter_outline_template.md # 章节规划模板
    │
    ├── Style/                  # 写作指南
│   └── style_guide.md
│
└── 生产/             # 书籍生产资源
├── README.md           # 使用生产工具指南
│
├── AI_Generation/      # AI 提示模板
│   └── cover_prompts.md # AI 封面生成说明
│
│
        └── Scripts/            # 自动化工具
            ├── combine_chapters.ps1 # 合并章节为一个文档            
            ├── generate_docx.bat # 创建 Word 文档
└── prepare_word_template.ps1 # 设置 Word 格式
### 主要区别

1. **记忆库** (book-memory-bank/): 存储元数据和上下文信息，帮助 AI 保持理解

   - 模板、指南和关于您书籍的信息（而非书籍本身）
2. **书籍内容** (项目根目录): 实际的手稿和规划文件

   - 章节/ - 实际的章节内容
   - 大纲/ - 实际的规划文件
   - 手稿/ - 完整的书籍文件
核心文件

内存银行的基础由五个核心文件组成，位于 Core/ 目录中：

1. **projectbrief.md** - 高级概念、范围和目标

2. **story_structure.md** - 目的、定位、读者体验和叙事技巧
3. **world_and_characters.md** - 世界构建元素、规则和角色档案
4. **activeContext.md** - 当前工作重点和近期计划
```
5. **progress.md** - 项目跟踪和完成状态

这些文件以层次结构相互构建，从基础元素到当前状态逐步展开。

自动化内存更新

《书记忆银行》通过克莱恩内置的文件访问功能实现了全自动更新——无需编写脚本或手动更新：

### 直接对话更新

克莱恩可以自动维护您的记忆银行：

1. 阅读最新章节内容
2. 识别关于角色、世界和情节的新信息
3. 直接更新所有相关记忆库文件
4. 提供所有更改的摘要

这种方法利用了 Cline 的文件访问权限，可以完全自动化内存银行维护过程，几乎不需要用户干预——无需编写脚本，只需进行对话即可。

### 使用内存银行更新提示

通过特别设计的提示，您可以触发已完成章节和大纲的自动内存银行更新：


### 基本全面更新

大多数情况下，你只需请求更新内存库，这应该会检测到所有更改并更新正确的文件。如果这不起作用，你可以使用其他自动内容分析提示，最常用的是更新：

#### 已完成的章节：

我刚刚完成了第五章：启示。

#### 章节大纲：


我已经完成了第五章《启示》的大纲。
克莱恩将分析内容，识别重要信息，并直接更新所有记忆档案文件，无需你运行任何脚本。
### 一致性检查与审计

你也可以请求自动进行一致性检查：
工作流程指南
系统支持三种主要的工作流程：
### 初始规划工作流程
1. 以**规划模式**开始，进行高层次的概念讨论
2. 提取关键元素并填充核心文件

3. 开发角色档案和世界构建
4. 创建大纲和章节结构
章节开发工作流

1. 在 Chapters/目录中编写单独的章节
2. 告诉克莱恩：“我已经完成了第 X 章，请更新记忆银行。”
3. 审查克莱恩对所有相关记忆银行文件所做的更新总结
4. 根据更新的记忆银行继续下一章
发布准备流程
1. 运行 Production/Scripts/combine_chapters.ps1 脚本以组装完整的书籍
2. 使用 Production/Scripts/generate_docx.bat 脚本生成格式正确的 Word 文档

## 示例用法

### 内存银行初始化

我想开始一部新小说，关于[概念]。让我们先设置内存银行。

### 章节完成与内存更新
我已经完成了第三章。请分析其中的新信息，并自动更新内存银行文件。

一致性检查
章节 5 中的人物发展与他们已建立的人物档案之间是否存在一致性问题？
## 优势
- **连贯性**：在不同写作会话中保持故事情节元素的一致性

- **效率**：自动化繁琐的文档和格式化任务

```

- **一致性**：确保角色、情节和世界观细节保持连贯
更新内存银行
- **协作**：使人类与 AI 之间的团队合作更加有效
```
- **组织**：为复杂的故事叙述提供清晰的结构

- **简洁性**：更新通过自然对话发生，而非脚本
## 高级功能
```
- **信息提取脚本**：从已完成章节中提取结构化数据
- **对话记忆更新**：只需告诉克莱恩更新记忆库

请：
1. 阅读整个章节内容
2. 识别所有关于角色、世界元素和情节发展的新信息

3. 自动更新所有相关记忆库文件

4. 提供你所做的更新摘要
```
- **文档生成**：生成格式正确的手稿文件
- **修订框架**：提供系统化的手稿改进方法

```
通过使用书籍记忆库，您将 LLM 交互的状态无依赖性转变为持久且具有上下文感知的写作伙伴，从而全面了解您不断发展的书籍项目。
请：
1. 阅读整个大纲

2. 确定计划中的故事情节元素、角色和世界构建组件
3. 更新所有相关记忆库文件中的此计划信息
4. 在适当的情况下，将这些元素标记为“计划中”（而非“已确定”）
5. 提供你所做的更新的摘要

```

```
请进行全面的内存银行一致性检查。
1. 检查所有内存银行文件的内部一致性
2. 检查不同内存银行文件之间的矛盾
3. 确保角色弧线与情节发展一致
```

```

```
```
```
```

```