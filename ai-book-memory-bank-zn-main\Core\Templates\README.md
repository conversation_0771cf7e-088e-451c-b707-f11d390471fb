# 模板目录

此目录包含模板文件，它们是创建新故事元素的起点。这些模板为项目中的文档提供结构化格式，以确保一致性。

## 可用模板

### 情节模板

- **master_outline_template.md** - 高级故事结构模板
- **chapter_outline_template.md** - 详细章节规划模板

这些模板为以下方面提供全面指导：
- 构建你的整体叙事
- 规划章节时保持细节一致
- 维持故事中的叙述连贯性

## 如何使用模板

1. **将模板复制**到你的工作位置（通常在主项目的 Outlines/目录中）
2. **根据你的项目命名规范重命名文件**（例如："Chapter01_Outline.md"）
3. **用你的具体内容填充各个部分**
4. **将完成的文件保存在你的项目工作目录中**

## 模板优势

- **一致性**：在所有章节和规划文档中保持相同详细程度
- **全面性**：确保考虑故事的所有重要方面
- **组织性**：保持规划文档结构清晰且易于导航
- **效率**：通过使用预构建框架节省时间

## 自定义模板

您可以自由修改这些模板，以更好地适应您的特定写作流程：

1. 将模板复制到新文件
2. 根据需要添加、删除或修改部分
3. 将您的定制模板保存以供将来使用

## 与记忆库集成

模板设计为与 Claude 的自动记忆更新系统无缝协作。当您根据这些模板完成章节大纲时，您可以请求 Claude 分析它并自动更新所有相关的记忆库文件。
