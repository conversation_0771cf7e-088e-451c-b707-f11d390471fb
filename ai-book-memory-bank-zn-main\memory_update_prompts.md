# 内存库更新提示

本文档提供即用型提示，指导 Claude 自动更新您的书籍内存库，无需任何脚本或手动文件管理。只需在写作过程中适当的时间复制并使用这些提示即可。

## 自动内容分析提示

###

理论上，在主要更新（新大纲或章节）后，各种更改应该更新到正确的内存库文件中。但这并不总是有效。你可以改用这个手动提示。
大多数情况下，你只需要请求更新内存库——这应该能检测到所有更改并更新正确的文件。如果这不起作用，你可以使用其他的自动内容分析提示。

```
更新内存库
```

### 完成一个章节后

```
我刚完成了第[X]章：[章节标题]

```

请：
1. 阅读整章内容
2. 识别所有关于角色、世界元素和情节发展的新信息
3. 自动更新所有相关记忆库文件
4. 提供你所做更新的摘要

确保检查以下内容：
- 新增或修改的角色特质、关系和动机
- 新增地点、物品或世界构建元素
- 情节发展，包括预示和为未来事件做铺垫
- 角色弧光或动机的变化
- 整体完成度更新

使用您的文件访问功能直接更新内存库文件，无需我运行任何脚本或复制粘贴信息。
```

### 完成一个章节大纲

```
我刚完成了第[X]章的提纲：[章节标题]。

请：
1. 阅读整个大纲
2. 确定计划中的故事元素、角色和世界构建组件
3. 使用这些计划信息更新所有相关的记忆库文件
4. 在适当的地方将这些元素标记为"计划中"（而非"已建立"）
5. 提供你所做更新的摘要

特别关注：
- 新引入的角色
- 新地点或设置将重点展示
- 计划中的情节发展及其影响
- 需要未来回报的设置元素
- 正在埋下的预示元素

在内存库中跟踪这些计划元素，以便我们在实际撰写章节时保持一致性。
```

### 用于特定故事元素

```
我们在最近的研究中确定了关于[元素类型]的重要新信息。

请使用这些关于[特定元素]的详细信息更新记忆库：
[列出关键细节或仅提及元素，让 Claude 进行分析]

使用您的文件访问权限：
1. 读取相关内存库文件
2. 确定这些更新应放置的位置
3. 使用 write_to_file 或 replace_in_file 直接更新文件
4. 确认所做的更改

尤其关注更新[相关文件，例如，Core/world_and_characters.md，Core/story_structure.md，等等]
```

## 一致性检查提示

### 一般一致性检查

```
请执行全面的内存库一致性检查。

1. 审查所有内存库文件以检查内部一致性
2. 检查不同内存库文件之间的矛盾
3. 确认角色弧线与剧情发展相符
4. 确保世界观元素一致应用
5. 检查最新发展是否正确整合

如发现任何不一致：
1. 确定具体问题
2. 提出解决方法
3. 经我批准，自动在内存库文件中修复不一致性
```

### 元素特定一致性检查

```
请检查与[角色/地点/情节元素]相关的连贯性问题。

具体来说，请验证：
1. 该元素在不同内存库文件中的所有提及
2. 该元素的时序一致性
3. 与其他元素的关系一致性
4. 与既定规则/系统的对齐

如果存在不一致，请识别它们并提出解决方案，然后在获得我的批准后直接更新文件。
```

## 进展与计划提示

### 更新进度跟踪

```
我们在项目中取得了显著进展。请：

1. 更新 Core/progress.md 文件，填写当前的完成百分比
2. 更新 Core/activeContext.md 文件，填写我们当前的关注点
3. 根据主提纲确定下一步要处理的部分
4. 总结已完成的工作和剩余的工作

使用您的文件访问功能，直接在记忆库文件中进行这些更新。
```

### 项目状态概述

```
根据当前内存库状态，请提供：

1. 到目前为止我们的进展总结
2. 关键角色及其当前状态
3. 主要剧情线索及其发展状态
4. 需要进一步发展的世界观元素
5. 推进项目的建议下一步

更新 Core/activeContext.md 文件，以便将来参考。
```

## 特殊更新提示

### 新角色介绍

```
我们新增了一个名为[Name]的角色。请：

1. 在 Core/world_and_characters.md 中创建或更新角色资料
2. 包含所有已建立的信息：
- 身体描述
   - 性格特点
   - 背景历史
   - 与其他角色的关系
- 目标和动机
   - 故事中的角色

3. 更新任何引用此角色的相关文件
4. 更新 Core/activeContext.md，将此角色包含在当前焦点中
```

### 世界构建扩展

```
我们通过关于[元素]的新信息扩展了世界构建。请：

1. 更新 Core/world_and_characters.md，添加以下详细信息：
   - [元素名称]
   - 描述和特性
   - 规则/限制
- 对故事的重要性
   - 与其他世界元素的关系

2. 确保与相关元素保持一致的交叉引用
3. 更新与此元素互动的角色资料
4. 将此元素整合到当前上下文中
```

### 故事发展更新

```
我们已就[情节元素]确立了重要的情节发展。请：

1. 使用这些发展更新主大纲
2. 记录对未来事件的任何预示或铺垫
3. 更新 Core/world_and_characters.md，其中包含受这些情节点影响的角色
4. 更新 Core/story_structure.md，记录任何叙事影响
5. 确保所有记忆库文件的时间线一致性
6. 更新 Core/activeContext.md，记录新的叙事方向
```

## 内存库审计提示

### 完整内存库审计

```
请对图书内存库进行全面审计：

1. 确认所有核心文件存在且结构正确
2. 确保所有内存库文件与最新内容保持同步
3. 检查是否存在任何空白、不一致或未充分开发的部分
4. 确定需要扩展或澄清的领域
5. 修复任何格式或结构问题

提供内存银行状态的报告，并直接对文件进行必要的更新。
```

### 文件特定审计

```
请审计[具体文件]内存库文件：

1. 确保其完整且最新
2. 检查格式和组织是否正确
3. 验证与其他内存库文件的一致性
4. 确定需要扩展的任何部分
5. 直接使用您的文件访问功能修复任何问题

报告您发现的内容和所做的更新。
```

## 使用这些提示的最佳实践

1. **尽可能具体**说明需要更新的元素
2. **提供背景信息**说明新信息是在哪里建立的
3. **查看 Claude 提供的关于更新内容的摘要**
4. **安排定期审核**以确保持续的内存库健康
5. **使用提示组合**处理复杂的更新场景

请记住，Claude 可以直接更新文件，无需您运行脚本或手动复制信息。这些提示利用了这一功能，以最小的努力保持您的内存库最新。
