{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code file changes and automatically analyzes the modified code for potential improvements, including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.cs", "**/*.php", "**/*.rb", "**/*.go", "**/*.rs", "**/*.swift", "**/*.kt", "**/*.scala", "**/*.vue", "**/*.svelte"]}, "then": {"type": "askAgent", "prompt": "A source code file has been modified. Please analyze the changes for code quality improvements while maintaining existing functionality. Focus on:\n\n1. **Code Smells Detection:**\n   - Long methods/functions (>50 lines)\n   - Complex conditional logic\n   - Duplicate code patterns\n   - Large classes or modules\n   - Poor naming conventions\n   - Magic numbers and hardcoded values\n\n2. **Design Patterns & Architecture:**\n   - Identify opportunities for common design patterns\n   - Suggest better separation of concerns\n   - Recommend appropriate abstraction levels\n   - Check for SOLID principles violations\n\n3. **Best Practices:**\n   - Error handling improvements\n   - Performance optimization opportunities\n   - Security considerations\n   - Memory usage optimization\n   - Async/await usage patterns\n\n4. **Readability & Maintainability:**\n   - Code documentation suggestions\n   - Variable and function naming improvements\n   - Code structure and organization\n   - Comment quality and necessity\n\n5. **Language-Specific Improvements:**\n   - Utilize modern language features\n   - Framework-specific best practices\n   - Type safety improvements (for typed languages)\n\nProvide specific, actionable suggestions with code examples where helpful. Prioritize improvements that enhance readability, maintainability, and performance without breaking existing functionality."}}