{"Favorite": "Favoriet", "Tag": "Label", "Duplicate": "Duplicaat", "Persona": "<PERSON>a", "Delete": "Verwijderen", "AI Response Configuration": "AI-responsconfiguratie", "AI Configuration panel will stay open": "Het AI-configuratiescher<PERSON> blijft open", "clickslidertips": "<PERSON>lik om waarden handmatig in te voeren.", "MAD LAB MODE ON": "MAD LAB-MODUS AAN", "Documentation on sampling parameters": "Documentatie over steekproefparameters", "kobldpresets": "Kobold voorinstellingen", "guikoboldaisettings": "KoboldAI-interface-instellingen", "Update current preset": "Huidige voorinstelling bijwerken", "Save preset as": "Instellingen opslaan als", "Import preset": "Voorinstelling importeren", "Export preset": "Voorinstelling exporteren", "Restore current preset": "Herstel huidige voorinstelling", "Delete the preset": "De voorinstelling verwijderen", "novelaipresets": "NovelAI-voorinstellingen", "Default": "Standaard", "openaipresets": "OpenAI-voorinstellingen", "Text Completion presets": "Tekstvervolledigingsvoorinstellingen", "AI Module": "AI-module", "Changes the style of the generated text.": "<PERSON><PERSON><PERSON> de stijl van de gegenereerde tekst.", "No Module": "Geen module", "Instruct": "Instrueren", "Prose Augmenter": "<PERSON><PERSON> Augment<PERSON>", "Text Adventure": "Tekst avontuur", "response legth(tokens)": "Reactielengte (tokens)", "Streaming": "Streaming", "Streaming_desc": "Toon de reactie beetje bij beetje zoals deze wordt gegenereerd", "context size(tokens)": "Contextgrootte (tokens)", "unlocked": "Ontgrendeld", "Only enable this if your model supports context sizes greater than 8192 tokens": "<PERSON><PERSON><PERSON> dit alleen in als uw model contextgroottes ondersteunt groter dan 8192 tokens", "Max prompt cost:": "Maximale promptkosten:", "Display the response bit by bit as it is generated.": "Toon het antwoord stuk voor stuk terwijl het wordt gegenereerd.", "When this is off, responses will be displayed all at once when they are complete.": "Als dit uit staat, worden reacties in één keer weergegeven wanneer ze compleet zijn.", "Temperature": "Temperatuur", "rep.pen": "Herhalingsstraf", "Rep. Pen. Range.": "<PERSON><PERSON><PERSON> <PERSON>.", "Rep. Pen. Slope": "<PERSON><PERSON> <PERSON>", "Rep. Pen. Freq.": "<PERSON><PERSON><PERSON>e<PERSON>.", "Rep. Pen. Presence": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "TFS": "TFS", "Phrase Repetition Penalty": "Straf voor zinsherhaling", "Off": "Uit", "Very light": "<PERSON><PERSON> licht", "Light": "Licht", "Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Aggressive": "Agressief", "Very aggressive": "Zeer agressief", "Unlocked Context Size": "Ontgrendelde Context Grootte", "Unrestricted maximum value for the context slider": "Onbeperkte maximale waarde voor de contextschuifregelaar", "Context Size (tokens)": "Contextgrootte (tokens)", "Max Response Length (tokens)": "Maximale lengte van het antwoord (tokens)", "Multiple swipes per generation": "Me<PERSON>ere swipes per generatie", "Enable OpenAI completion streaming": "OpenAI voltooiingsstreaming inschakelen", "Frequency Penalty": "Frequentieboete", "Presence Penalty": "Aanwezigheidsboete", "Count Penalty": "Tel straf", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Herhaling Straf", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "Snelle Prompt Bewerking", "Main": "Hoofd", "NSFW": "NSFW", "Jailbreak": "Jailbreak", "Utility Prompts": "Hulpprogramma Prompts", "Impersonation prompt": "Imitatie-prompt", "Restore default prompt": "Standaardprompt herstellen", "Prompt that is used for Impersonation function": "Prompt die wordt gebruikt voor de imitatie-functie", "World Info Format Template": "Wereldinfo-formaat s<PERSON>", "Restore default format": "Standaardformaat herstellen", "Wraps activated World Info entries before inserting into the prompt.": "Verpakt geactiveerde Wereldinfo-items voordat deze in de prompt worden ingevoegd.", "scenario_format_template_part_1": "Gebruik", "scenario_format_template_part_2": "om een ​​plaats te markeren waar de inhoud wordt ingevoegd.", "Scenario Format Template": "Sjabloon voor scenario-indeling", "Personality Format Template": "Sjabloon voor persoonlijkheidsformaat", "Group Nudge Prompt Template": "Sjabloon voor groepsaanmoedigingsprompt", "Sent at the end of the group chat history to force reply from a specific character.": "Verzonden aan het einde van de geschiedenis van de groepschat om een ​​antwoord van een specifiek personage af te dwingen.", "New Chat": "<PERSON><PERSON><PERSON> g<PERSON>p<PERSON>", "Restore new chat prompt": "<PERSON><PERSON><PERSON> nieuwe chatprompt", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Ingesteld aan het begin van de chatgeschiedenis om aan te geven dat er een nieuwe chat gaat starten.", "New Group Chat": "Nieuwe groepschat", "Restore new group chat prompt": "Standaardprompt herstellen", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Ingesteld aan het begin van de chatgeschiedenis om aan te geven dat er een nieuwe groepschat gaat starten.", "New Example Chat": "Nieuwe voorbeeldchat", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Ingesteld aan het begin van Dialoogvoorbeelden om aan te geven dat er een nieuwe voorbeeldchat gaat starten.", "Continue nudge": "<PERSON>a door met duwen", "Set at the end of the chat history when the continue button is pressed.": "Ingesteld aan het einde van de chatgeschiedenis wanneer op de knop Doorgaan wordt gedrukt.", "Replace empty message": "<PERSON><PERSON><PERSON><PERSON> leeg be<PERSON>t", "Send this text instead of nothing when the text box is empty.": "<PERSON><PERSON>ur deze tekst in plaats van niets wanneer het tekstvak leeg is.", "Seed": "<PERSON><PERSON><PERSON>", "Set to get deterministic results. Use -1 for random seed.": "Ingesteld om deterministische resultaten te verkrijgen. Gebruik -1 voor willekeurig zaad.", "Temperature controls the randomness in token selection": "Temperatuur regelt de willekeurigheid bij het selecteren van tokens", "Top_K_desc": "Top K stelt een maximumhoeveelheid top tokens in die kunnen worden gekozen", "Top_P_desc": "Top P (ook bekend als kernsampling)", "Typical P": "Typisch P", "Typical_P_desc": "Typische P-sampling geeft prioriteit aan tokens op basis van hun afwijking van de gemiddelde entropie van de set", "Min_P_desc": "Min P stelt een basismimimumkans in", "Top_A_desc": "Top A stelt een drempel in voor tokenselectie op basis van het kwadraat van de ho<PERSON>ste tokenkans", "Tail_Free_Sampling_desc": "Staartvrije sampling (TFS)", "rep.pen range": "Herhalingsstrafbereik", "Mirostat": "Mirosta", "Mode": "Modus", "Mirostat_Mode_desc": "Een waarde van 0 schakelt Mirostat volledig uit. 1 is voor Mirostat 1.0 en 2 is voor Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "Regelt de variabiliteit van Mirostat-uitvoer", "Eta": "Eta", "Mirostat_Eta_desc": "Regelt <PERSON> le<PERSON><PERSON><PERSON><PERSON> van Mirostat", "Ban EOS Token": "Ban EOS-token", "Ban_EOS_Token_desc": "Verbied het End-of-Sequence (EOS) token met KoboldCpp (en mogelijk ook andere tokens met KoboldAI).\rGoed voor het schrij<PERSON> van ver<PERSON>, maar mag niet worden gebruikt voor chat- en instructiemodus.", "GBNF Grammar": "GBNF Grammatica", "Type in the desired custom grammar": "Typ de gewenste aangepaste grammatica", "Samplers Order": "Samplers Volgorde", "Samplers will be applied in a top-down order. Use with caution.": "Samplers worden toegepast in een top-down volgorde. <PERSON><PERSON><PERSON><PERSON> met v<PERSON><PERSON>chtigheid.", "Tail Free Sampling": "Staartvrije sampling", "Load koboldcpp order": "<PERSON><PERSON> k<PERSON>-bestelling", "Preamble": "Preambule", "Use style tags to modify the writing style of the output.": "Gebruik stijltags om de schrijfstijl van de uitvoer te wijzigen.", "Banned Tokens": "Verboden tokens", "Sequences you don't want to appear in the output. One per line.": "Sequenties die je niet in de uitvoer wilt laten verschijnen. <PERSON><PERSON> per regel.", "Logit Bias": "Logit Bias", "Add": "Toevoegen", "Helps to ban or reenforce the usage of certain words": "Helpt bij het verbieden of versterken van het gebruik van bepaalde woorden", "CFG Scale": "CFG Schaal", "Negative Prompt": "Negatieve prompt", "Add text here that would make the AI generate things you don't want in your outputs.": "Voeg hier tekst toe die ervoor zou zorgen dat de AI dingen genereert die je niet wilt in je uitvoer.", "Used if CFG Scale is unset globally, per chat or character": "Wordt gebruikt als CFG-scha<PERSON> wereldwi<PERSON>d, per chat of karakter niet is ingesteld.", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "Mirostat LR", "Min Length": "Minimale lengte", "Top K Sampling": "Top K-Bemonstering", "Nucleus Sampling": "<PERSON><PERSON><PERSON><PERSON>", "Top A Sampling": "Top A-Bemonstering", "CFG": "CFG", "Neutralize Samplers": "<PERSON>eutral<PERSON><PERSON>", "Set all samplers to their neutral/disabled state.": "Stel alle samplers in op hun neutrale/uitgeschakelde toestand.", "Sampler Select": "Sampler selecteren", "Customize displayed samplers or add custom samplers.": "Pas weergegeven samplers aan of voeg aangepaste samplers toe.", "Epsilon Cutoff": "Epsilon-afkapwaarde", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon-cutoff stelt een kansdrempel in waaronder tokens worden uitgesloten van be<PERSON>tering", "Eta Cutoff": "Eta-afkapwaarde", "Eta_Cutoff_desc": "Eta-cutoff is de belangrijkste parameter van de speciale Eta Bemonsteringstechniek.&#13;In eenheden van 1e-4; een redelijke waarde is 3.&#13;<PERSON><PERSON> in op 0 om uit te schakelen.&#13;Zie het artikel Truncation Sampling as Language Model Desmoothing <PERSON> et al. (2022) voor details.", "rep.pen decay": "Rep-pen verval", "Encoder Rep. Pen.": "Encoder her<PERSON>ss<PERSON>f", "No Repeat Ngram Size": "<PERSON><PERSON> her<PERSON>e ng<PERSON>g<PERSON>", "Skew": "<PERSON><PERSON><PERSON>", "Max Tokens Second": "Maximale Tokens per Seconde", "Smooth Sampling": "<PERSON><PERSON><PERSON> bemons<PERSON>", "Smooth_Sampling_desc": "Hiermee kunt u kwadratische/kubieke transformaties gebruiken om de verdeling aan te passen. Lagere waarden voor de afvlakkingsfactor zullen creatiever zijn, meestal tussen 0,2 en 0,3 is de sweetspot (ervan uit<PERSON>ande dat de curve = 1 is). Hogere waarden voor de Smoothing Curve maken de curve steiler, waardoor keuzes met een lage waarschijnlijkheid agressiever worden bestraft. Een curve van 1,0 komt overeen met alleen het gebruik van de afvlakkingsfactor.", "Smoothing Factor": "Gladstrijkfactor", "Smoothing Curve": "Gladmakende curve", "DRY_Repetition_Penalty_desc": "DRY bestraft tokens die het einde van de invoer zouden verlengen in een reeks die eerder in de invoer heeft plaatsgevonden. Zet de vermenigvuldiger op 0 om uit te schakelen.", "DRY Repetition Penalty": "DROOG Herhalingsstraf", "DRY_Multiplier_desc": "Stel in op waarde > 0 om DROGEN in te schakelen. Regelt de omvang van de straf voor de kortst bestrafte reeksen.", "Multiplier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DRY_Base_desc": "<PERSON>pa<PERSON>t hoe snel de straf toeneemt naarmate de reeks langer wordt.", "Base": "<PERSON><PERSON>", "DRY_Allowed_Length_desc": "De langste reeks die kan worden herhaald zonder te worden bestraft.", "Allowed Length": "<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "Penalty Range": "Strafbereik", "DRY_Sequence_Breakers_desc": "Tokens waarover het matchen van de reeksen niet wordt voortgezet. Opgegeven als een door komma's gesche<PERSON> lijst met tekenreeksen tussen aanhalingstekens.", "Sequence Breakers": "Sequentiebrekers", "JSON-serialized array of strings.": "JSON-g<PERSON><PERSON><PERSON><PERSON><PERSON> reeks strings.", "Dynamic Temperature": "Dynamische Temperatuur", "Scale Temperature dynamically per token, based on the variation of probabilities": "Pas temperatuur dynamisch toe per token, op basis van de variatie van kansen", "Minimum Temp": "Minimale temperatuur", "Maximum Temp": "Maximale temperatuur", "Exponent": "Exponent", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (modus=1 is alleen voor llama.cpp)", "Mirostat_desc": "Mirostat is een thermostaat voor de outputperplexiteit", "Mirostat Mode": "Mirostat-modus", "Variability parameter for Mirostat outputs": "Variabiliteitsparameter voor Mirostat-uitvoer", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "<PERSON><PERSON><PERSON><PERSON><PERSON> Mir<PERSON>", "Beam search": "Beam-zoeken", "Helpful tip coming soon.": "Handige tip volgt binnenkort.", "Number of Beams": "Aantal beams", "Length Penalty": "Lengteboete", "Early Stopping": "Vroegtijdig stoppen", "Contrastive search": "Contrastie<PERSON>", "Penalty Alpha": "<PERSON><PERSON><PERSON>", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Sterkte van de regulariseringsterm voor contrastieve zoekopdrachten. Stel in op 0 om CS uit te schakelen.", "Do Sample": "Monster", "Add BOS Token": "Voeg B<PERSON>-token toe", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Voeg het BOS-token toe aan het begin van prompts. Het uitschakelen hiervan kan de antwoorden creatiever maken", "Ban the eos_token. This forces the model to never end the generation prematurely": "Ban het EOS-token. Dit dwingt het model om de generatie nooit voortijdig te beëindigen", "Ignore EOS Token": "<PERSON><PERSON><PERSON>-token", "Ignore the EOS Token even if it generates.": "<PERSON><PERSON>er het EOS-token, zelfs als het wordt gegenereerd.", "Skip Special Tokens": "Speciale tokens overslaan", "Temperature Last": "Laatste temperatuur", "Temperature_Last_desc": "Gebruik de temperatuursampler als laatste", "Speculative Ngram": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Use a different speculative decoding method without a draft model": "Gebruik een andere speculatieve decoderingsmethode zonder conceptmodel.\rHet gebruik van een conceptmodel heeft de voorkeur. Speculatief ngram is niet zo effectief.", "Spaces Between Special Tokens": "<PERSON><PERSON><PERSON><PERSON> tussen speciale tokens", "LLaMA / Mistral / Yi models only": "Alleen <PERSON> / Mistral / Yi-modellen", "Example: some text [42, 69, 1337]": "Voorbeeld: wat tekst [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Klassificatorv<PERSON><PERSON> beg<PERSON>. Meer nuttige tips volgen binnenkort", "Scale": "<PERSON><PERSON><PERSON>", "JSON Schema": "JSON-schema", "Type in the desired JSON schema": "Typ het gewenste JSON-schema", "Grammar String": "Grammaticareeks", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF of EBNF, hangt af van de gebruikte backend. Als u dit gebruikt, moet u weten welke.", "Top P & Min P": "Top P & Min P", "Load default order": "Standaardvolgorde laden", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "alleen lama.cpp. Bepaalt de volgorde van de samplers. Als de Mirostat-modus niet 0 is, wordt de samplervolgorde genegeerd.", "Sampler Priority": "Prioriteit van de sampler", "Ooba only. Determines the order of samplers.": "Alleen O<PERSON>a. Bepaalt de volgorde van samplers.", "Character Names Behavior": "Karakternamen Gedrag", "Helps the model to associate messages with characters.": "Helpt het model be<PERSON>ten aan karakters te koppelen.", "None": "<PERSON><PERSON>", "character_names_default": "Behalve voor groepen en vroegere persona's. Zorg er anders voor dat u namen opgeeft in de prompt.", "Don't add character names.": "Voeg geen namen van personages toe.", "Completion": "Voltooiingsobject", "character_names_completion": "<PERSON>r zijn be<PERSON><PERSON><PERSON> van toepassing: alleen Latijnse alfanumerieke tekens en onderstrepingstekens. Werkt niet voor alle bronnen, met name: <PERSON>, MistralAI, Google.", "Add character names to completion objects.": "Voeg karakternamen toe aan voltooiingsobjecten.", "Message Content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prepend character names to message contents.": "Voeg tekennamen toe aan de inhoud van het bericht.", "Continue Postfix": "Ga door met Postfix", "The next chunk of the continued message will be appended using this as a separator.": "Het volgende deel van het vervolgbericht wordt toegevoegd en gebruikt dit als scheidingsteken.", "Space": "<PERSON><PERSON><PERSON><PERSON>", "Newline": "<PERSON><PERSON><PERSON> lijn", "Double Newline": "<PERSON><PERSON>e nieuwe lijn", "Wrap user messages in quotes before sending": "<PERSON><PERSON><PERSON> gebruike<PERSON>berich<PERSON> in aanhalingstekens voordat u ze verzendt", "Wrap in Quotes": "In Quotes plaatsen", "Wrap entire user message in quotes before sending.": "Wikkel het volledige gebruikersbericht in aanhalingstekens voordat u het verzendt.", "Leave off if you use quotes manually for speech.": "Laat dit weg als u handmatig aanhalingstekens gebruikt voor spraak.", "Continue prefill": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "Continue sends the last message as assistant role instead of system message with instruction.": "Doorgaan stuurt het laatste bericht als assistentrol in plaats van een systeember<PERSON>t met instructie.", "Squash system messages": "Systeemberichten samenvoegen", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Combineert opeenvolgende systeemberichten tot één (exclusief voorbeeld dialogen). Kan de coherentie verbeteren voor sommige modellen.", "Enable function calling": "<PERSON><PERSON><PERSON> in", "Send inline images": "Inline afbeeldingen verzenden", "image_inlining_hint_1": "Verzendt afbeeldingen in prompts als het model dit ondersteunt.\n                                                Gebruik de", "image_inlining_hint_2": "actie op elk bericht of de", "image_inlining_hint_3": "menu om een ​​afbeeldingsbestand aan de chat toe te voegen.", "Inline Image Quality": "Inline-beeldkwaliteit", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "Laag", "openai_inline_image_quality_high": "<PERSON><PERSON>", "Use AI21 Tokenizer": "Gebruik AI21 Tokenizer", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Gebruik de juiste tokenizer voor Jurassic-modellen, die efficiënter is dan GP<PERSON>'s.", "Use Google Tokenizer": "Google Tokenizer gebruiken", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Gebruik de juiste tokenizer voor Google-modellen via hun API. Langzamere promptverwerking, maar biedt veel nauwkeuriger token-telling.", "Use system prompt": "Gebruik systeemprompt", "(Gemini 1.5 Pro/Flash only)": "(alleen <PERSON> 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Voegt alle systeemberichten samen tot aan het eerste bericht met een niet-systeemrol, en verzendt ze in een", "Merges_all_system_messages_desc_2": "veld.", "Assistant Prefill": "<PERSON>sist<PERSON>", "Start Claude's answer with...": "Start het antwo<PERSON> van <PERSON> met...", "Assistant Impersonation Prefill": "Vooraf invullen van assistent-imitatie", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Verzend de systeemprompt voor ondersteunde modellen. Als dit is uitgeschakeld, wordt het gebruikersbericht toegevoegd aan het begin van de prompt.", "User first message": "<PERSON><PERSON><PERSON> van de gebruiker e<PERSON>t", "Restore User first message": "Her<PERSON>l gebruiker eerste bericht", "Human message": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, instructie, enz.\n<PERSON>oegt niets toe als het leeg is, d.w.z. vereist een nieuwe prompt met de rol 'gebruiker'.", "New preset": "Nieuwe voorinstelling", "Delete preset": "Voorinstelling verwijderen", "View / Edit bias preset": "Biasvoorinstelling bekijken / bewerken", "Add bias entry": "Biasvermelding toevoegen", "Most tokens have a leading space.": "De meeste tokens hebben een leidende spatie.", "API Connections": "API-verbindingen", "Text Completion": "Tekstvoltooiing", "Chat Completion": "Chat-voltooiing", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Vermijd het verzenden van gevoelige informatie naar de Horde.", "Review the Privacy statement": "Bekijk de privacyverklaring", "Register a Horde account for faster queue times": "Registreer een Horde-account voor snellere wachttijden", "Learn how to contribute your idle GPU cycles to the Horde": "<PERSON><PERSON> hoe je je ongebruikte GPU-cycli kunt bijdragen aan de <PERSON>", "Adjust context size to worker capabilities": "<PERSON><PERSON> <PERSON> aan aan de mogelijkheden van de werknemer", "Adjust response length to worker capabilities": "<PERSON><PERSON> de le<PERSON>te van de reactie aan aan de mogelijkheden van de werknemer", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Kan helpen bij slechte reacties door alleen de goedgekeurde werknemers in de wachtrij te zetten. Kan de responstijd vertragen.", "Trusted workers only": "Alleen vertrouwde werknemers", "API key": "API-sleutel", "Get it here:": "Haal het hier:", "Register": "Registreren", "View my Kudos": "<PERSON><PERSON>", "Enter": "Invoeren", "to use anonymous mode.": "om de anonieme modus te gebruiken.", "Clear your API key": "Wis uw API-sleutel", "For privacy reasons, your API key will be hidden after you reload the page.": "Om privacyredenen wordt uw API-sleutel verborgen nadat u de pagina opnieuw hebt geladen.", "Models": "<PERSON><PERSON>", "Refresh models": "<PERSON><PERSON>", "-- Horde models not loaded --": "-- Horde-modellen niet geladen --", "Not connected...": "Niet verbonden...", "API url": "API-url", "Example: http://127.0.0.1:5000/api ": "Voorbeeld: http://127.0.0.1:5000/api", "Connect": "Verbinden", "Cancel": "<PERSON><PERSON><PERSON>", "Novel API key": "NovelAPI-sleutel", "Get your NovelAI API Key": "Ontvang uw NovelAI API-sleutel", "Enter it in the box below": "<PERSON><PERSON><PERSON> het in het vak hieronder in", "Novel AI Model": "Novel AI-model", "No connection...": "<PERSON>n verbinding...", "API Type": "API-type", "Default (completions compatible)": "Standaard [OpenAI /completions compatibel: oobabooga, LM Studio, etc.]", "TogetherAI API Key": "TogetherAI API-sleutel", "TogetherAI Model": "TogetherAI-model", "-- Connect to the API --": "-- Verbinding maken met de API --", "OpenRouter API Key": "OpenRouter API-sleutel", "Click Authorize below or get the key from": "Klik op Autoriseren hieronder of haal de sleutel op bij", "View Remaining Credits": "Bekijk het resterende krediet", "OpenRouter Model": "OpenRouter-model", "Model Providers": "<PERSON><PERSON><PERSON><PERSON>", "InfermaticAI API Key": "InfermaticAI API-sleutel", "InfermaticAI Model": "InfermaticAI-model", "DreamGen API key": "DreamGen API-sleutel", "DreamGen Model": "DreamGen-model", "Mancer API key": "Mancer <PERSON>-sleutel", "Mancer Model": "Mancer-model", "Make sure you run it with": "<PERSON>org ervoor dat u het uitvoert met", "flag": "vlag", "API key (optional)": "API-sleutel (optioneel)", "Server url": "Server-URL", "Example: http://127.0.0.1:5000": "Voorbeeld: http://127.0.0.1:5000", "Custom model (optional)": "Aangepast model (optioneel)", "vllm-project/vllm": "vllm-project/vllm (OpenAI API-wrappermodus)", "vLLM API key": "vLLM API-sleutel", "Example: http://127.0.0.1:8000": "Voorbeeld: http://127.0.0.1:8000", "vLLM Model": "vLLM-model", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Wrappermodus voor OpenAI API)", "Aphrodite API key": "Aphrodite API-sleutel", "Aphrodite Model": "Aphrodite-model", "ggerganov/llama.cpp": "ggerganov/llama.cpp (Output-server)", "Example: http://127.0.0.1:8080": "Voorbeeld: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Voorbeeld: http://127.0.0.1:11434", "Ollama Model": "Ollama-model", "Download": "Downloaden", "Tabby API key": "Tabby <PERSON>-sleutel", "koboldcpp API key (optional)": "koboldcpp API-sleutel (optioneel)", "Example: http://127.0.0.1:5001": "Voorbeeld: http://127.0.0.1:5001", "Authorize": "Toestemming geven", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Haal uw OpenRouter API-token op met behulp van OAuth-flow. U wordt doorgestuurd naar openrouter.ai", "Bypass status check": "Omzeil statuscontrole", "Chat Completion Source": "Bron voor Chatvoltooiing", "Reverse Proxy": "Omgekeerde proxy", "Proxy Presets": "Proxy-voorinstellingen", "Saved addresses and passwords.": "Opgeslagen adressen en wachtwoorden.", "Save Proxy": "Proxy opslaan", "Delete Proxy": "Proxy verwijderen", "Proxy Name": "Proxy-naam", "This will show up as your saved preset.": "Dit wordt weergegeven als uw opgeslagen voorinstelling.", "Proxy Server URL": "URL van proxyserver", "Alternative server URL (leave empty to use the default value).": "Alternatieve server-URL (laat leeg om de standaardwaarde te gebruiken).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Verwijder je echte OAI API-sleutel uit het API-paneel VOORDAT je iets in dit vak typt", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "We kunnen geen ondersteuning bieden voor problemen die zich voordoen bij het gebruik van een niet-officiële OpenAI-proxy", "Doesn't work? Try adding": "Werkt niet? <PERSON><PERSON>r toe te voegen", "at the end!": "aan het einde!", "Proxy Password": "Proxywachtwoord", "Will be used as a password for the proxy instead of API key.": "Wordt gebruikt als wachtwoord voor de proxy in plaats van API-sleutel.", "Peek a password": "<PERSON><PERSON> een wachtwoord", "OpenAI API key": "OpenAI API-sleutel", "View API Usage Metrics": "Bekijk API-gebruiksstatistieken", "Follow": "Volgen", "these directions": "deze instructies", "to get your OpenAI API key.": "om uw OpenAI API-sleutel te krijgen.", "Use Proxy password field instead. This input will be ignored.": "Gebruik in plaats daarvan het veld 'Proxywachtwoord'. Deze invoer wordt genegeerd.", "OpenAI Model": "OpenAI-model", "Bypass API status check": "Omzeil API-statuscontrole", "Show External models (provided by API)": "Externe modellen weergeven (geleverd door API)", "Get your key from": "<PERSON><PERSON> je sleutel op bij", "Anthropic's developer console": "<PERSON>thropic's on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Claude Model": "Claude-model", "Window AI Model": "Window AI-model", "Model Order": "<PERSON><PERSON><PERSON> OpenRouter-modellen", "Alphabetically": "Alfabetisch", "Price": "<PERSON><PERSON><PERSON><PERSON> (goedkoopste)", "Context Size": "<PERSON>textgrootte", "Group by vendors": "Groeperen op leveranciers", "Group by vendors Description": "Plaats OpenAI-modellen in é<PERSON> groep, antropische modellen in een andere groep, enz. Kan worden gecombineerd met sorteren.", "Allow fallback routes": "Fallback-routes <PERSON><PERSON><PERSON>", "Allow fallback routes Description": "Het alternatieve model wordt automatisch gekozen als het geselecteerde model niet aan uw verzoek kan voldoen.", "AI21 API Key": "AI21 API-sleutel", "AI21 Model": "AI21-model", "MakerSuite API Key": "MakerSuite API-sleutel", "Google Model": "Google-model", "MistralAI API Key": "MistralAI API-sleutel", "MistralAI Model": "MistralAI-model", "Groq API Key": "Groq API-sleutel", "Groq Model": "Groq-model", "Perplexity API Key": "Perplexity API-sleutel", "Perplexity Model": "Verbijstering Model", "Cohere API Key": "Cohere API-sleutel", "Cohere Model": "Cohere-model", "Custom Endpoint (Base URL)": "Aangepast eindpunt (basis-URL)", "Custom API Key": "Aangepaste API-sleutel", "Available Models": "Beschikbare modellen", "Prompt Post-Processing": "Snelle naverwerking", "Applies additional processing to the prompt before sending it to the API.": "Past extra verwerking toe op de prompt voordat deze naar de API wordt verzonden.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Verifieert uw API-verbinding door een kort testbericht te verzenden. Wees ervan bewust dat u hiervoor wordt gecrediteerd!", "Test Message": "Testbericht", "Auto-connect to Last Server": "Automatisch verbinden met de laatste server", "Missing key": "❌ Ontbrekende sleutel", "Key saved": "✔️ Sleutel opgeslagen", "View hidden API keys": "Verborgen API-sleutels bekijken", "AI Response Formatting": "AI-respons opmaken", "Advanced Formatting": "<PERSON><PERSON><PERSON><PERSON>", "Context Template": "Contextsjabloon", "Auto-select this preset for Instruct Mode": "Automatisch deze voorinstelling selecteren voor Instructiemodus", "Story String": "Verhaalstring", "Example Separator": "Voorbeeldscheider", "Chat Start": "Chatstart", "Add Chat Start and Example Separator to a list of stopping strings.": "Voeg Chat Start en Voorbeeldscheidingsteken toe aan een lijst met stoptekenreeksen.", "Use as Stop Strings": "Gebruik als stopreeksen", "Allow Jailbreak": "<PERSON><PERSON><PERSON>", "Context Order": "Contextvolgorde", "Summary": "<PERSON><PERSON><PERSON><PERSON>", "Author's Note": "Aantekening van de auteur", "Example Dialogues": "Voorbeelddialogen", "Hint": "Tip:", "In-Chat Position not affected": "Bestellingen met samenvattingen en notities van de auteur worden alleen beïnvloed als er geen in-chatpositie voor is ingesteld.", "Instruct Mode": "Instructiemo<PERSON>", "Enabled": "Ingeschakeld", "instruct_bind_to_context": "<PERSON><PERSON> ing<PERSON>, worden contextsjablonen automatisch geselecteerd op basis van de geselecteerde Instruct-sja<PERSON><PERSON>onnaam of op basis van uw voorkeur.", "Bind to Context": "Binden aan context", "Presets": "Voorinstellingen", "Auto-select this preset on API connection": "Automatisch deze voorinstelling selecteren bij API-verbinding", "Activation Regex": "Activeringsregex", "Wrap Sequences with Newline": "Sequenties o<PERSON><PERSON><PERSON><PERSON><PERSON> met nieu<PERSON> regel", "Replace Macro in Sequences": "Macro vervangen in sequenties", "Skip Example Dialogues Formatting": "<PERSON><PERSON><PERSON> v<PERSON>an", "Include Names": "Namen opnemen", "Force for Groups and Personas": "<PERSON><PERSON> voor groepen en persona's", "System Prompt": "Sys<PERSON>mprom<PERSON>", "Instruct Mode Sequences": "Sequenties in instructiemodus", "System Prompt Wrapping": "Systeempromptverpakking", "Inserted before a System prompt.": "Ingevoegd vóór een systeemprompt.", "System Prompt Prefix": "Systeempromptvoorvoegsel", "Inserted after a System prompt.": "Ingevoegd na een systeemprompt.", "System Prompt Suffix": "Achtervoegsel voor systeemprompt", "Chat Messages Wrapping": "Chatberichten inpakken", "Inserted before a User message and as a last prompt line when impersonating.": "Wordt ingevoegd vóór een gebruikersbericht en als laatste promptregel bij het nabootsen van identiteit.", "User Message Prefix": "Voorvoegsel voor gebruikersbericht", "Inserted after a User message.": "Ingevoegd na een gebruikersbericht.", "User Message Suffix": "Achtervoegsel voor gebruikersbericht", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Wordt ingevoegd vóór een Assistent-bericht en als laatste promptregel bij het genereren van een AI-antwoord.", "Assistant Message Prefix": "Assistent-berichtvoorvoegsel", "Inserted after an Assistant message.": "Ingevoegd na een Assistent-bericht.", "Assistant Message Suffix": "Assistent-berichtachtervoegsel", "Inserted before a System (added by slash commands or extensions) message.": "Ingevoegd vóór een <PERSON> (toegevoegd door slash-opdrachten of extensies).", "System Message Prefix": "Systeemberichtvoorvoegsel", "Inserted after a System message.": "Ingevoegd na een systeembericht.", "System Message Suffix": "Systeemberichtachtervoegsel", "If enabled, System Sequences will be the same as User Sequences.": "<PERSON><PERSON> ing<PERSON>, zijn <PERSON>teemreeksen hetzelfde als Gebruikersreeksen.", "System same as User": "Systeem hetzelfde als Gebruiker", "Misc. Sequences": "<PERSON><PERSON><PERSON> Opeenvolgingen", "Inserted before the first Assistant's message.": "Ingevoegd vóór het eerste bericht van de Assistent.", "First Assistant Prefix": "Eerste assistent-voorvoegsel", "instruct_last_output_sequence": "Ingevoegd vóór het laatste bericht van de Assistent of als laatste promptregel bij het genereren van een AI-antwoord (behalve een neutrale/systeemrol).", "Last Assistant Prefix": "Laatste assistent-voorvoegsel", "Will be inserted as a last prompt line when using system/neutral generation.": "<PERSON>t ingevoegd als laatste promptregel bij gebruik van systeem/neutrale generatie.", "System Instruction Prefix": "Systeeminstructievoorvoegsel", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Als er een stopreeks wordt gegenereerd, wordt alles daarboven uit de uitvoer verwijderd (inclusief).", "Stop Sequence": "Stopsequentie", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "<PERSON>t aan het begin van de chatgeschiedenis ingevoegd als deze niet begint met een geb<PERSON>ikers<PERSON><PERSON>.", "User Filler Message": "<PERSON><PERSON><PERSON> van de gebruiker-invuller", "Context Formatting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "(Saved to Context Template)": "(Opgeslagen in contextsjabloon)", "Always add character's name to prompt": "Voeg altijd de naam van het personage toe aan de prompt", "Generate only one line per request": "<PERSON><PERSON> slechts één regel per verzoek", "Trim Incomplete Sentences": "Onvolledige Zinnen Trimmen", "Include Newline": "Nieuwe Regel Inclusief", "Misc. Settings": "Diverse instellingen", "Collapse Consecutive Newlines": "Samenvouwen van opeenvolgende nieuwe regels", "Trim spaces": "Spaties trimmen", "Tokenizer": "Tokenizer", "Token Padding": "Token-vulling", "Start Reply With": "<PERSON><PERSON> antwoord met", "AI reply prefix": "AI antwoord voorvoegsel", "Show reply prefix in chat": "Toon antwoordvoor<PERSON>egsel in chat", "Non-markdown strings": "Niet-markdown-strings", "separate with commas w/o space between": "<PERSON><PERSON><PERSON><PERSON> met komma's zonder spatie er<PERSON>sen", "Custom Stopping Strings": "Aangepaste Stop<PERSON>en", "JSON serialized array of strings": "JSON geserialise<PERSON>e reeks van strings", "Replace Macro in Stop Strings": "<PERSON>ro vervangen in aangepaste stopreeksen", "Auto-Continue": "Automatisch doorgaan", "Allow for Chat Completion APIs": "Chatvervolledigings-API's toes<PERSON><PERSON>", "Target length (tokens)": "<PERSON><PERSON><PERSON><PERSON> (tokens)", "World Info": "Wereldinformatie", "Locked = World Editor will stay open": "Vergrendeld = Wereldeditor blijft open", "Worlds/Lorebooks": "Werelden/Lorebooks", "Active World(s) for all chats": "<PERSON><PERSON><PERSON>(en) voor alle chats", "-- World Info not found --": "-- Wereldinformatie niet gevonden --", "Global World Info/Lorebook activation settings": "Global World Info/Lorebook-activeringsinstellingen", "Click to expand": "Klik om uit te breiden", "Scan Depth": "Scandiepte", "Context %": "Context %", "Budget Cap": "Budgetplafond", "(0 = disabled)": "(0 = uitgeschakeld)", "Scan chronologically until reached min entries or token budget.": "Scan chronologisch tot het minimum aantal inzendingen of het tokenbudget is bereikt.", "Min Activations": "Minimale activeringen", "Max Depth": "Maximale diepte", "(0 = unlimited, use budget)": "(0 = onbeperkt, gebruik budget)", "Insertion Strategy": "Invoegstrategie", "Sorted Evenly": "Gelijkmatig gesorteerd", "Character Lore First": "Karakter Lore Eerst", "Global Lore First": "Globale Lore Eerst", "Entries can activate other entries by mentioning their keywords": "Items kunnen andere items activeren door hun trefwoorden te vermelden", "Recursive Scan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Lookup for the entry keys in the context will respect the case": "Op<PERSON><PERSON><PERSON> van de itemtoetsen in de context zal de zaak respecteren", "Case Sensitive": "Hoofdlettergevoelig", "If the entry key consists of only one word, it would not be matched as part of other words": "Als de itemtoets uit slechts één woord bestaat, wordt deze niet gematcht als onderdeel van andere woorden", "Match Whole Words": "<PERSON><PERSON> woorden matchen", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "<PERSON><PERSON> met het grootste aantal sleutelovereenkomsten worden geselecteerd voor het filteren van Inclusiegroepen", "Use Group Scoring": "Gebruik groepsscores", "Alert if your world info is greater than the allocated budget.": "Waar<PERSON><PERSON>w als uw wereldinformatie groter is dan het toegewezen budget.", "Alert On Overflow": "Waarschuwing bij overloop", "New": "<PERSON><PERSON><PERSON>", "or": "of", "--- Pick to Edit ---": "--- <PERSON><PERSON> om te bewerken ---", "Rename World Info": "Wereldinformatie hernoemen", "Open all Entries": "Alle items openen", "Close all Entries": "Alle items sluiten", "New Entry": "Nieuw item", "Fill empty Memo/Titles with Keywords": "Vul lege Memo/Titels in met trefwoorden", "Import World Info": "Wereldinformatie importeren", "Export World Info": "Wereldinformatie exporteren", "Duplicate World Info": "Wereldinformatie dup<PERSON>ren", "Delete World Info": "Wereldinformatie verwijderen", "Search...": "Zoeken...", "Search": "Zoe<PERSON><PERSON>dracht", "Priority": "Prioriteit", "Custom": "Aangepast", "Title A-Z": "Titel A-Z", "Title Z-A": "Titel Z-A", "Tokens ↗": "Tokens ↗", "Tokens ↘": "Tokens ↘", "Depth ↗": "Diepte ↗", "Depth ↘": "Diepte ↘", "Order ↗": "Bestelling ↗", "Order ↘": "Bestelling ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Trigger% ↗", "Trigger% ↘": "Trigger% ↘", "Refresh": "<PERSON><PERSON><PERSON><PERSON>", "User Settings": "Gebruikersinstellingen", "Simple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced": "Geavanceerd", "UI Language": "Taal", "Account": "Rekening", "Admin Panel": "<PERSON><PERSON><PERSON><PERSON>", "Logout": "Uitloggen", "Search Settings": "<PERSON><PERSON><PERSON>ling<PERSON>", "UI Theme": "UI-thema", "Import a theme file": "Importeer een themabestand", "Export a theme file": "Exporteer een themabe<PERSON>", "Delete a theme": "<PERSON>er<PERSON><PERSON><PERSON> een thema", "Update a theme file": "Werk een themabestand bij", "Save as a new theme": "Opslaan als nieuw thema", "Avatar Style:": "Avatarstijl", "Circle": "<PERSON><PERSON><PERSON>", "Square": "Vierkan<PERSON>", "Rectangle": "Rechthoek", "Chat Style:": "Chatstijl:", "Flat": "<PERSON><PERSON>ub<PERSON>s\nDocument", "Bubbles": "Bub<PERSON>s", "Document": "Document", "Specify colors for your theme.": "<PERSON><PERSON> kleuren op voor uw thema.", "Theme Colors": "<PERSON><PERSON>", "Main Text": "Hoofdtekst", "Italics Text": "<PERSON><PERSON><PERSON>", "Underlined Text": "Onderstreepte tekst", "Quote Text": "Quote-tekst", "Shadow Color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Background": "<PERSON><PERSON>", "UI Background": "UI-achtergrond", "UI Border": "UI-grens", "User Message Blur Tint": "Gebruikersbericht Blur Tint", "AI Message Blur Tint": "AI-bericht Blur Tint", "Chat Width": "Chatbreedte", "Width of the main chat window in % of screen width": "<PERSON><PERSON><PERSON> van het hoofdchatvenster in % van de schermbreedte", "Font Scale": "Lettertype schaal", "Font size": "Lettertypegrootte", "Blur Strength": "Vervagingssterkte", "Blur strength on UI panels.": "Vervagingssterkte op UI-panelen.", "Text Shadow Width": "Tekstschaduwbreedte", "Strength of the text shadows": "<PERSON><PERSON><PERSON><PERSON> van de tekstschaduwen", "Disables animations and transitions": "Schakelt animaties en overgangen uit", "Reduced Motion": "<PERSON>ermind<PERSON><PERSON> beweging", "removes blur from window backgrounds": "Verwijdert vervaging van vensterachtergronden", "No Blur Effect": "<PERSON><PERSON> vervagingseffect", "Remove text shadow effect": "Verwijder tekstschaduweffect", "No Text Shadows": "<PERSON><PERSON>", "Reduce chat height, and put a static sprite behind the chat window": "Verlaag de hoogte van de chat en plaats een statische sprite achter het chatvenster", "Waifu Mode": "Waifu-modus", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "<PERSON>n altijd de volledige lijst met berichtacties voor chatberichten, in plaats van ze te verbergen achter '...' ", "Auto-Expand Message Actions": "Automatisch uitbreiden van berichtacties", "Alternative UI for numeric sampling parameters with fewer steps": "Alternatieve UI voor numerieke bemonsteringsparameters met minder stappen", "Zen Sliders": "Zen-schuiven", "Entirely unrestrict all numeric sampling parameters": "Volledig alle numerieke bemonsteringsparameters onbeperkt maken", "Mad Lab Mode": "Mad Lab-modus", "Time the AI's message generation, and show the duration in the chat log": "<PERSON> de berichtgeneratie van de AI en toon de duur in het chatlogboek", "Message Timer": "Berichtentimer", "Show a timestamp for each message in the chat log": "Toon een tijdstempel voor elk bericht in het chatlogboek", "Chat Timestamps": "Chat-tijdstempels", "Show an icon for the API that generated the message": "Toon een pictogram voor de API die het bericht heeft gegenereerd", "Model Icon": "Modelpictogram", "Show sequential message numbers in the chat log": "Toon opeenvolgende berichtnummers in het chatlogboek", "Message IDs": "Bericht-IDs", "Hide avatars in chat messages.": "Verberg avatars in chatberichten.", "Hide Chat Avatars": "Chatavatars verbergen", "Show the number of tokens in each message in the chat log": "Toon het aantal tokens in elk bericht in het chatlogboek", "Show Message Token Count": "Toon bericht token tellen", "Single-row message input area. Mobile only, no effect on PC": "Enkele rij bericht invoergebied. Alleen mobiel, geen effect op pc", "Compact Input Area (Mobile)": "Compacte invoergebied (Mobiel)", "In the Character Management panel, show quick selection buttons for favorited characters": "In het karakterbehee<PERSON><PERSON><PERSON>, toon snelle selectieknoppen voor favoriete karakters", "Characters Hotswap": "Personages Hotswap", "Enable magnification for zoomed avatar display.": "<PERSON><PERSON><PERSON> vergroting in voor ingezoomde avatarweergave.", "Avatar Hover Magnification": "Vergroting van avatar-zweving", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Schak<PERSON>t een vergrotingseffect in bij het aanwi<PERSON><PERSON> van de muis wanneer u de ingezoomde avatar weergeeft nadat u in de chat op de afbeelding van een avatar hebt geklikt.", "Show tagged character folders in the character list": "<PERSON><PERSON> gemark<PERSON><PERSON> in de lijst met personages", "Tags as Folders": "Tags als mappen", "Tags_as_Folders_desc": "Recente wijziging: <PERSON>s moeten als mappen worden gemarkeerd in het menu Tagbeheer om als zodanig te verschijnen. <PERSON><PERSON> hier om het naar voren te brengen.", "Character Handling": "Personagebehandeling", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Als dit is ingesteld in de geavanceerde karakterdefinities, wordt dit veld weergegeven in de lijst met personages.", "Char List Subheader": "Subko<PERSON> van Char-lijst", "Character Version": "Karakterversie", "Created by": "Gemaakt door", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Gebruik fuzzy-matching en zoek personages in de lijst op alle gegevensvelden, niet alleen op een naamsubreeks", "Advanced Character Search": "Geavanceerd personage zoeken", "If checked and the character card contains a prompt override (System Prompt), use that instead": "<PERSON><PERSON> aangev<PERSON> en de karakter<PERSON> bevat een prompt-override (Systeemprompt), g<PERSON><PERSON><PERSON> die in plaats daarvan", "Prefer Character Card Prompt": "<PERSON><PERSON><PERSON><PERSON> prompt", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "<PERSON><PERSON> a<PERSON> en de karakter<PERSON> bevat een jailbreak-override (Post History Instruction), g<PERSON><PERSON><PERSON> die in plaats daarvan", "Prefer Character Card Jailbreak": "<PERSON><PERSON><PERSON><PERSON> jailbreak", "never_resize_avatars_tooltip": "Vermijd het bijsnijden en vergroten/verkleinen van geïmporteerde karakterafbeeldingen. Indien uitgeschakeld, bijsnijden/formaat wijzigen naar 512 x 768.", "Never resize avatars": "Avatars nooit verkleinen", "Show actual file names on the disk, in the characters list display only": "<PERSON>n de werkelijke bestandsnamen op de schijf, alleen in de weergave van de lij<PERSON> met personages", "Show avatar filenames": "Toon avatar bestand<PERSON><PERSON>n", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Prompt om ingesloten kaarttags te importeren bij het importeren van personages. Anders worden ingesloten tags genegeerd", "Import Card Tags": "<PERSON><PERSON> tags importeren", "Hide character definitions from the editor panel behind a spoiler button": "Verberg karakterdefinities uit het bewerkingspaneel achter een spoilerknop", "Spoiler Free Mode": "Spoiler Free-modus", "Miscellaneous": "<PERSON><PERSON><PERSON>", "Reload and redraw the currently open chat": "<PERSON><PERSON> de momenteel geopende chat opnieuw en teken opnieuw", "Reload Chat": "<PERSON>t herladen", "Debug Menu": "Debugmenu", "Smooth Streaming": "Vlotte streaming", "Experimental feature. May not work for all backends.": "Experimentele functie. Werkt mogelijk niet voor alle backends.", "Slow": "<PERSON><PERSON><PERSON>", "Fast": "Snel", "Play a sound when a message generation finishes": "Speel een geluid af wanneer een berichtgeneratie is voltooid", "Message Sound": "Berichtgeluid", "Only play a sound when ST's browser tab is unfocused": "<PERSON><PERSON><PERSON> alleen een geluid af wanneer het browsertabblad van ST niet gefocust is", "Background Sound Only": "<PERSON><PERSON>", "Reduce the formatting requirements on API URLs": "<PERSON>erminder de opmaakvereisten voor API-URL's", "Relaxed API URLS": "Ontspannen API URL'S", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Vraag om de Wereldinformatie/Lorebook te importeren voor elke nieuwe karakter met ingesloten lorebook. Als dit niet is aangevinkt, wordt in plaats daarvan een kort bericht weergegeven", "Lorebook Import Dialog": "Lorebook-import dialoogvenster", "Restore unsaved user input on page refresh": "<PERSON><PERSON><PERSON> niet-opgeslagen gebruikersinvoer bij het vern<PERSON><PERSON> van de pagina", "Restore User Input": "Gebruikersinvoer herstellen", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Sta het verplaatsen van bepaalde UI-elementen toe door ze te slepen. Alleen pc, geen effect op mobiel", "Movable UI Panels": "Verplaatsbare UI-panelen", "MovingUI preset. Predefined/saved draggable positions": "MovingUI-voorinstelling. Voorgedefinieerde/opgeslagen sleepbare posities", "MUI Preset": "MUI-voorinstelling", "Save movingUI changes to a new file": "Sla de wijzigingen van MovingUI op in een nieuw bestand", "Reset MovingUI panel sizes/locations.": "Reset MovingUI-paneelformaten/locaties.", "Apply a custom CSS style to all of the ST GUI": "Pas een aangepaste CSS-stijl toe op de hele ST GUI", "Custom CSS": "Aangepaste CSS", "Expand the editor": "De editor uit<PERSON><PERSON>", "Chat/Message Handling": "Chat-/berichtafhandeling", "# Messages to Load": "# Bericht Laden", "The number of chat history messages to load before pagination.": "Het aantal chatgeschiedenisberichten dat moet worden geladen vóór paginering.", "(0 = All)": "(0 = Alles)", "Streaming FPS": "Streaming FPS", "Update speed of streamed text.": "Update de s<PERSON><PERSON><PERSON> van gestreamde tekst.", "Example Messages Behavior": "Gedrag voorbeeldberichten", "Gradual push-out": "Geleidelijke uitstoot", "Always include examples": "Altijd voorbeelden opnemen", "Never include examples": "Nooit voorbeelden opnemen", "Send on Enter": "Verzenden bij Enter", "Disabled": "Uitgeschakeld", "Automatic (PC)": "<PERSON>mat<PERSON> (PC)", "Press Send to continue": "Druk op Verzenden om door te gaan", "Show a button in the input area to ask the AI to continue (extend) its last message": "Toon een knop in het invoergebied om de AI te vragen om door te gaan (uit te breiden) met zijn la<PERSON><PERSON> bericht", "Quick 'Continue' button": "<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON>' knop", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Toon pijlk<PERSON>ppen op het laatste in-chatbericht om alternatieve AI-responses te genereren. Zowel pc als mobiel", "Swipes": "Veegbewegingen", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Sta het gebruik van veeggebaren toe op het laatste in-chatbericht om swipe-generatie te activeren. <PERSON><PERSON> mobiel, geen effect op pc", "Gestures": "Geb<PERSON><PERSON>", "Auto-load Last Chat": "Laatste chat automatisch laden", "Auto-scroll Chat": "Automatisch scrollen chat", "Save edits to messages without confirmation as you type": "Sla bewerkingen op berichten op zonder bevestiging terwijl u typt", "Auto-save Message Edits": "Automatisch opslaan van berichtbewerkingen", "Confirm message deletion": "Bevestig bericht verwijdering", "Auto-fix Markdown": "Automatisch repareren van Markdown", "Disallow embedded media from other domains in chat messages": "Ingesloten media van andere domeinen niet toes<PERSON> ​​in chatberichten.", "Forbid External Media": "Externe media verbieden", "Allow {{char}}: in bot messages": "<PERSON><PERSON>an {{char}}: in botberichten", "Allow {{user}}: in bot messages": "<PERSON><PERSON>an {{user}}: in botberichten", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Codering en -tekens overslaan in berichttekst, waardoor een subset van HTML-opmaak en Markdown wordt toegestaan", "Show tags in responses": "Tags weergeven in reacties", "Allow AI messages in groups to contain lines spoken by other group members": "Sta toe dat AI-berichten in groepen regels bevatten die zijn uitgesproken door andere groepsleden", "Relax message trim in Groups": "Trimbericht ontspannen in groepen", "Log prompts to console": "Logt prompts naar console", "Requests logprobs from the API for the Token Probabilities feature": "Vraagt logprobs aan van de API voor de Token Probabilities-functie", "Request token probabilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatically reject and re-generate AI message based on configurable criteria": "Automatisch AI-bericht afwijzen en opnieuw genereren op basis van configureerbare criteria", "Auto-swipe": "Automatisch vegen", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Schakel de automatische-vegen functie in. Instellingen in dit gedeelte hebben alleen effect wanneer automatisch vegen is ingeschakeld", "Minimum generated message length": "Minimale gegenereerde berichtlengte", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Als het gegenereerde bericht korter is dan dit, activeer dan een automatische veeg", "Blacklisted words": "Verboden woorden", "words you dont want generated separated by comma ','": "woorden die je niet gegenereerd wilt hebben gescheiden door komma ','", "Blacklisted word count to swipe": "Aantal verboden woorden om te vegen", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Minimaal aantal gedetecteerde verboden woorden om een automatische veeg te activeren", "AutoComplete Settings": "Instellingen voor automatisch aanvullen", "Automatically hide details": "Details automatisch verbergen", "Determines how entries are found for autocomplete.": "Bepaalt hoe vermeldingen worden gevonden voor automatisch aanvullen.", "Autocomplete Matching": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> passen", "Starts with": "Begint met", "Includes": "Inclusief", "Fuzzy": "Wazig", "Sets the style of the autocomplete.": "Stelt de stijl van het automatisch aanvullen in.", "Autocomplete Style": "<PERSON><PERSON><PERSON><PERSON>", "Follow Theme": "Volg Thema", "Dark": "<PERSON><PERSON>", "Sets the font size of the autocomplete.": "<PERSON><PERSON><PERSON> de lettergrootte van automatisch aanvullen in.", "Sets the width of the autocomplete.": "Stelt de breedte van het automatisch aanvullen in.", "Autocomplete Width": "<PERSON><PERSON><PERSON>", "chat input box": "chat-invoervak", "entire chat width": "g<PERSON><PERSON>", "full window width": "volledige raambreedte", "STscript Settings": "STscript-instellingen", "Sets default flags for the STscript parser.": "Stelt standaardvlaggen in voor de STscript-parser.", "Parser Flags": "Parser-vlaggen", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "<PERSON><PERSON><PERSON> over naar een strengere ontsnapping, waardoor alle begrenzende tekens kunnen worden geëscaped met een backslash, en ook backslashes kunnen worden geëscaped.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Vervang alle {{getvar::}} en {{getglobalvar::}} macro's door variabelen met een bereik om dubbele macrovervanging te voorkomen.", "REPLACE_GETVAR": "VERVANG_GETVAR", "Change Background Image": "Achtergrondafbeelding wijzigen", "Filter": "Filter", "Automatically select a background based on the chat context": "Automatisch een achtergrond selecteren op basis van de chatcontext", "Auto-select": "Automatisch selecteren", "System Backgrounds": "Systeemachtergronden", "Chat Backgrounds": "Achtergronden chat", "bg_chat_hint_1": "Chatachtergronden gegenereerd met de", "bg_chat_hint_2": "extensie zal hier verschijnen.", "Extensions": "Uitbreidingen", "Notify on extension updates": "Op de hoogte stellen van extensie-updates", "Manage extensions": "<PERSON><PERSON><PERSON> extensies", "Import Extension From Git Repo": "Extensie importeren vanuit Git Repository", "Install extension": "Installeer extensie", "Extras API:": "Extra's API:", "Auto-connect": "Automatisch verbinden", "Extras API URL": "Extra's API-URL", "Extras API key (optional)": "Extra API-sleutel (optioneel)", "Persona Management": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "How do I use this?": "Hoe gebruik ik dit?", "Click for stats!": "Klik voor statistieken!", "Usage Stats": "Gebruiksstatistieken", "Backup your personas to a file": "Maak een back-up van je persona's naar een bestand", "Backup": "Back-up", "Restore your personas from a file": "<PERSON><PERSON><PERSON> je persona's uit een bestand", "Restore": "Herstellen", "Create a dummy persona": "Maak een dummy-persona", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Toggle grid view": "Rasterweergave omzetten", "No persona description": "[<PERSON><PERSON>]", "Name": "<PERSON><PERSON>", "Enter your name": "<PERSON><PERSON><PERSON> je naam in", "Click to set a new User Name": "<PERSON>lik om een nieuwe gebruikersnaam in te stellen", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Klik om uw geselecteerde persona aan de huidige chat te vergrendelen. Klik opnieuw om het slot te verwijderen.", "Click to set user name for all messages": "Klik om een gebruikersnaam in te stellen voor alle berichten", "Persona Description": "Persoonbeschrijving", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Voorbeeld: [{{user}} is een 28-jarig <PERSON><PERSON> ka<PERSON>.]", "Tokens persona description": "Beschrijving van tokens", "Position:": "Positie:", "In Story String / Prompt Manager": "In Verhaalreeks / Prompt Manager", "Top of Author's Note": "<PERSON><PERSON><PERSON> <PERSON><PERSON>'s Notitie", "Bottom of Author's Note": "On<PERSON><PERSON> <PERSON><PERSON> van Au<PERSON>ur's Notitie", "In-chat @ Depth": "In-chat @ Diepte", "Depth:": "Diepte:", "Role:": "Rol:", "System": "Systeem", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Assistant": "Assistent", "Show notifications on switching personas": "Notificaties tonen bij het wisselen van personages", "Character Management": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Locked = Character Management panel will stay open": "Vergrendeld = Paneel voor personagebeheer blijft open", "Select/Create Characters": "Personages selecteren/maken", "Favorite characters to add them to HotSwaps": "Favoriete personages toe<PERSON><PERSON>n aan HotSwaps", "Token counts may be inaccurate and provided just for reference.": "Token-tellingen kunnen onnauwkeurig zijn en worden alleen ter referentie verstrekt.", "Total tokens": "Totaal tokens", "Calculating...": "<PERSON><PERSON><PERSON><PERSON>...", "Tokens": "Tokens", "Permanent tokens": "Permanente tokens", "Permanent": "Permanent", "About Token 'Limits'": "Over token 'limieten'", "Toggle character info panel": "Sc<PERSON><PERSON> het karakterinformatiepaneel in of uit", "Name this character": "<PERSON><PERSON> dit personage een naam", "extension_token_counter": "Munten:", "Click to select a new avatar for this character": "Klik om een nieuwe avatar voor dit personage te selecteren", "Add to Favorites": "Toevoegen aan favorieten", "Advanced Definition": "Geavance<PERSON><PERSON> definitie", "Character Lore": "Personage-acht<PERSON>grond<PERSON><PERSON><PERSON>", "Chat Lore": "Chat Lore", "Export and Download": "Exporteren en downloaden", "Duplicate Character": "Personage dupliceren", "Create Character": "Personage maken", "Delete Character": "Personage verwijderen", "More...": "Meer...", "Link to World Info": "<PERSON> naar Wereldinformatie", "Import Card Lore": "<PERSON><PERSON>", "Scenario Override": "<PERSON><PERSON><PERSON>", "Convert to Persona": "Converteren naar Persona", "Rename": "<PERSON><PERSON><PERSON><PERSON>", "Link to Source": "<PERSON> naar bron", "Replace / Update": "Vervangen/bijwerken", "Import Tags": "Importeer labels", "Search / Create Tags": "Zoeken / Tags maken", "View all tags": "Alle tags bekijken", "Creator's Notes": "Maker's Notities", "Show / Hide Description and First Message": "Beschrijving en eerste bericht weergeven/verbergen", "Character Description": "Karakterbeschrijving", "Click to allow/forbid the use of external media for this character.": "Klik om het gebruik van externe media voor dit personage toe te staan/te verbieden.", "Ext. Media": "Ext. Media", "Describe your character's physical and mental traits here.": "<PERSON><PERSON><PERSON><PERSON><PERSON> hier de fysieke en mentale eigenschappen van je personage.", "First message": "Eerste bericht", "Click to set additional greeting messages": "Klik om extra begroetingsberichten in te stellen", "Alt. Greetings": "Alt. Groeten", "This will be the first message from the character that starts every chat.": "Dit zal het eerste bericht zijn van het personage dat elke chat begint.", "Group Controls": "Groepsbesturing", "Chat Name (Optional)": "<PERSON><PERSON> (Optioneel)", "Click to select a new avatar for this group": "Klik om een nieuwe avatar voor deze groep te selecteren", "Group reply strategy": "Groepsantwoordstrategie", "Natural order": "Natuurlijke volgorde", "List order": "Lijstvolgorde", "Group generation handling mode": "Verwerkingsmodus voor het genereren van groepen", "Swap character cards": "Wissel karakterkaarten uit", "Join character cards (exclude muted)": "<PERSON>luit je aan bij ka<PERSON> (exclusief gedempt)", "Join character cards (include muted)": "<PERSON>luit je aan bij ka<PERSON> (inclusief gedempt)", "Inserted before each part of the joined fields.": "Ingevoegd vóór elk deel van de samengevoegde velden.", "Join Prefix": "Sluit je aan bij <PERSON>vo<PERSON>", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Wanneer 'Verbind karakterkaarten' is geselecteerd, worden alle respectieve velden van de karakters samengevoegd.\rDit betekent dat in de verhaalreeks bijvoorbeeld alle karakterbeschrijvingen worden samengevoegd tot één grote tekst.\rAls u wilt dat deze velden gescheiden worden, kunt u hier een voor- of achtervoegsel definiëren.\r\rDeze waarde ondersteunt normale macro's en zal ook {{char}} vervangen door de relevante naam van het char en <FIELDNAME> door de naam van het onderdeel (bijvoorbeeld: beschrijving, persoonlijkheid, scenario, etc.)", "Inserted after each part of the joined fields.": "Ingevoegd na elk deel van de samengevoegde velden.", "Join Suffix": "Sluit je aan bij het achtervo<PERSON>sel", "Set a group chat scenario": "Stel een scenario voor groepschat in", "Click to allow/forbid the use of external media for this group.": "Klik om het gebruik van externe media voor deze groep toe te staan/te verbieden.", "Restore collage avatar": "Collage-avatar <PERSON><PERSON><PERSON>", "Allow self responses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Auto Mode": "Automatische modus", "Auto Mode delay": "Vertraging automatische modus", "Hide Muted Member Sprites": "Verberg gedempte ledensprites", "Current Members": "<PERSON><PERSON><PERSON> leden", "Add Members": "Leden toe<PERSON>n", "Create New Character": "Nieuw personage maken", "Import Character from File": "Personage importeren uit bestand", "Import content from external URL": "In<PERSON>d importeren van externe URL", "Create New Chat Group": "<PERSON><PERSON><PERSON> chatgroep maken", "Characters sorting order": "Sorteervolgor<PERSON> van personages", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "Nieuwste", "Oldest": "Oudste", "Favorites": "Favorieten", "Recent": "Recent", "Most chats": "Me<PERSON> chats", "Least chats": "<PERSON><PERSON> chats", "Most tokens": "Meeste tokens", "Least tokens": "Minste tokens", "Random": "<PERSON><PERSON><PERSON><PERSON>", "Toggle character grid view": "Wissel weergave roosterkarakter", "Bulk_edit_characters": "Massaal bewerken personages", "Bulk select all characters": "Selecteer alle tekens in bulk", "Bulk delete characters": "Massaal verwijderen personages", "popup-button-save": "<PERSON><PERSON>", "popup-button-yes": "<PERSON>a", "popup-button-no": "<PERSON><PERSON>", "popup-button-cancel": "<PERSON><PERSON><PERSON>", "popup-button-import": "Importeren", "Advanced Definitions": "Geavanceerde definities", "Prompt Overrides": "Prompt-overschrijvingen", "(For Chat Completion and Instruct Mode)": "(<PERSON><PERSON> vol<PERSON> van chat en instructiemodus)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Voeg {{original}} in in elk vak in om de respectievelijke standaardprompt vanuit systeeminstellingen op te nemen.", "Main Prompt": "Hoofd Prompt", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "<PERSON><PERSON> inhoud hier zal de standaard Hoofd Prompt vervangen die wordt gebruikt voor dit personage. (v2 spec: systeem_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "<PERSON><PERSON> inhoud hier zal de standaard Jailbreak Prompt vervangen die wordt gebruikt voor dit personage. (v2 spec: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Maker's Metadata (<PERSON><PERSON> met de AI prompt)", "Creator's Metadata": "Metagegevens van <PERSON>", "(Not sent with the AI Prompt)": "(<PERSON><PERSON> met de <PERSON>-prompt)", "Everything here is optional": "Alles hier is optioneel", "(Botmaker's name / Contact Info)": "(<PERSON><PERSON> / Contactgegevens)", "(If you want to track character versions)": "(Al<PERSON> je versies van het personage wilt bijhouden)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON>, of lijst de chatmodellen op waarop het is getest. Dit zal worden weergegeven in de lijst met personages.)", "Tags to Embed": "In te sluiten Tags", "(Write a comma-separated list of tags)": "(<PERSON>hri<PERSON><PERSON> een komma-gescheiden lijst met tags)", "Personality summary": "Persoonlijkheidssamenvatting", "(A brief description of the personality)": "(<PERSON><PERSON> korte beschrij<PERSON> van de persoonlijkheid)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(<PERSON>ms<PERSON><PERSON>gh<PERSON><PERSON> en context van de interactie)", "Character's Note": "Karakternotitie", "(Text to be inserted in-chat @ designated depth and role)": "(Te<PERSON>t die in de chat moet worden ingevoegd @ aangegeven diepte en rol)", "@ Depth": "@ Diepte", "Role": "Rol", "Talkativeness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "How often the character speaks in group chats!": "Hoe vaak het personage spreekt in groepschats!", "How often the character speaks in": "Hoe vaak het personage spreekt", "group chats!": "groepschats!", "Shy": "Verlegen", "Normal": "Normaal", "Chatty": "K<PERSON><PERSON><PERSON>", "Examples of dialogue": "<PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON>", "Important to set the character's writing style.": "Belangrijk om de schrijfstijl van het personage in te stellen.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(<PERSON><PERSON><PERSON><PERSON><PERSON> van chatdialogen. Begin elk voorbeeld met START op een nieuwe regel.)", "Save": "Opsla<PERSON>", "Chat History": "Chatgeschiedenis", "Import Chat": "Chat importeren", "Copy to system backgrounds": "Kopiëren naar systeemachtergronden", "Rename background": "<PERSON><PERSON><PERSON><PERSON><PERSON> herno<PERSON>en", "Lock": "Slot", "Unlock": "Ontgrendelen", "Delete background": "Achtergrond verwijderen", "Chat Scenario Override": "Chatscenario negeren", "Remove": "Verwijderen", "Type here...": "Typ hier...", "Chat Lorebook": "Chat Loreboek voor", "Chat Lorebook for": "Chat Loreboek voor", "chat_world_template_txt": "<PERSON>en geselecteerde Wereldinfo wordt aan deze chat gekoppeld. Bij het genereren van een AI-antwoord,\n                    het zal worden gecombineerd met de inzendingen uit globale en karakterboeken.", "Select a World Info file for": "Selecteer een Wereldinformatiebestand voor", "Primary Lorebook": "Primaire Verhalenboek", "A selected World Info will be bound to this character as its own Lorebook.": "<PERSON>en geselecteerde Wereldinformatie zal aan dit personage worden gekoppeld als zijn eigen Verhalenboek.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Bij het genereren van een AI-antwoord zal het worden gecombineerd met de invoer uit een wereldwijde Wereldinformatie-selector.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Het exporteren van een personage zou ook het geselecteerde Verhalenboekbestand exporteren dat is ingebed in de JSON-gegevens.", "Additional Lorebooks": "Extra Verhalenboeken", "Associate one or more auxillary Lorebooks with this character.": "<PERSON><PERSON> een of meer extra Verhalenboeken aan dit personage.", "NOTE: These choices are optional and won't be preserved on character export!": "LET OP: Deze keuzes zijn optioneel en worden niet behouden bij het exporteren van het personage!", "Rename chat file": "Chatbestand hernoemen", "Export JSONL chat file": "Exporteer JSONL-chatbestand", "Download chat as plain text document": "Download chat als plat tekstbestand", "Delete chat file": "Chatbestand verwijderen", "Use tag as folder": "Taggen als map", "Hide on character card": "Verbergen op karakterkaart", "Delete tag": "Tag verwijderen", "Entry Title/Memo": "Titel/Memo", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "WI-invoerstatus:\r🔵Constant\r🟢 Normaal\r🔗 Gevectoriseerd\r❌ Uitgeschakeld", "WI_Entry_Status_Constant": "<PERSON><PERSON><PERSON>", "WI_Entry_Status_Normal": "Normaal", "WI_Entry_Status_Vectorized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WI_Entry_Status_Disabled": "Gehandicapt", "T_Position": "↑Char: voor karakterdefinities\n↓Char: na karakterdefinities\n↑AN: voor aantekeningen auteur\n↓AN: na aantekeningen auteur\n@D: op diepte", "Before Char Defs": "Voor karakterdefinities", "After Char Defs": "Na karakterdefinities", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Voor AN", "After AN": "Na AN", "at Depth System": "@D⚙️", "at Depth User": "@D👤", "at Depth AI": "@D🤖", "Depth": "Diepte", "Order:": "Bestelling:", "Order": "Bestelling:", "Trigger %:": "Trekker %:", "Probability": "Waarschijnlijkheid", "Duplicate world info entry": "Dubbele invoer van wereldinformatie", "Delete world info entry": "Verwijder wereldinfo-item", "Comma separated (required)": "Komma gescheiden (vereist)", "Primary Keywords": "Primaire trefwoorden", "Keywords or Regexes": "Trefwoorden of Regexes", "Comma separated list": "Door komma's gescheiden lijst", "Switch to plaintext mode": "<PERSON><PERSON><PERSON> over naar de le<PERSON><PERSON>e teks<PERSON><PERSON><PERSON>", "Logic": "Logica", "AND ANY": "EN ENIGE", "AND ALL": "EN ALLES", "NOT ALL": "NIET ALLES", "NOT ANY": "NIET ENIGE", "(ignored if empty)": "(genegeerd indien leeg)", "Optional Filter": "Optioneel filter", "Keywords or Regexes (ignored if empty)": "Trefwoorden of Regexes (genegeerd indien leeg)", "Comma separated list (ignored if empty)": "<PERSON> komma's gescheiden lijst (genegeerd als leeg)", "Use global setting": "Gebruik de algemene instelling", "Case-Sensitive": "Hoofdlettergevoelig", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Can be used to automatically activate Quick Replies": "Kan worden gebruikt om Quick Replies automatisch te activeren", "Automation ID": "Automatiserings-ID", "( None )": "( <PERSON><PERSON> )", "Content": "<PERSON><PERSON><PERSON>", "Exclude from recursion": "Uitsluite<PERSON> van recursie", "Prevent further recursion (this entry will not activate others)": "Voorkom verdere recursie (dit item activeert andere niet)", "Delay until recursion (this entry can only be activated on recursive checking)": "Vertraging tot recursie (deze invoer kan alleen worden geactiveerd bij recursieve controle)", "What this keyword should mean to the AI, sent verbatim": "Wat dit trefwoord voor de AI zou moeten betekenen, woordelijk verzonden", "Filter to Character(s)": "Filteren op personage(s)", "Character Exclusion": "Personage uitsluiting", "-- Characters not found --": "-- Personages niet gevonden --", "Inclusion Group": "Insluitingsgroep", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Inclusiegroepen zorgen ervoor dat slechts één item uit een groep tegelijk wordt geactiveerd, als er meerdere worden geactiveerd.\rOndersteunt meerdere door komma's gescheiden groepen.\r\rDocumentatie: World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Geef prioriteit aan deze invoer: Als dit selectievakje is aang<PERSON><PERSON><PERSON>, krijgt deze invoer prioriteit boven alle selecties. Als er meerdere items prioriteit hebben, wordt de invoer met de hoogste 'volgorde' gekozen.", "Only one entry with the same label will be activated": "Slechts één item met hetzelfde label zal worden geactiveerd", "A relative likelihood of entry activation within the group": "<PERSON><PERSON> relatieve waarschijnlijkheid van activatie binnen de groep", "Group Weight": "Groepsgewicht", "Selective": "Selectief", "Use Probability": "Gebruik Waarschijnlijkheid", "Add Memo": "<PERSON><PERSON>", "Text or token ids": "Tekst of [token-ID's]", "close": "dichtbij", "prompt_manager_edit": "Bewerking", "prompt_manager_name": "<PERSON><PERSON>", "A name for this prompt.": "<PERSON>en naam voor deze prompt.", "To whom this message will be attributed.": "Aan wie dit bericht wordt toegeschreven.", "AI Assistant": "AI-assistent", "prompt_manager_position": "<PERSON><PERSON><PERSON>", "Next to other prompts (relative) or in-chat (absolute).": "Naast andere prompts (relatief) of in-chat (absoluut).", "prompt_manager_relative": "Familielid", "prompt_manager_depth": "Diepte", "0 = after the last message, 1 = before the last message, etc.": "0 = na het laatste bericht, 1 = voor het laatste bericht, etc.", "Prompt": "Prompt", "The prompt to be sent.": "De prompt die verzonden moet worden.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Deze prompt kan niet worden overschreven door karakterkaarten, zelfs als overschrijvingen de voorkeur hebben.", "prompt_manager_forbid_overrides": "Overschrijvingen verbieden", "reset": "opnieuw instellen", "save": "redden", "This message is invisible for the AI": "<PERSON><PERSON> bericht is onzichtbaar voor de AI", "Message Actions": "Berichtacties", "Translate message": "Bericht vertalen", "Generate Image": "Afbeelding genereren", "Narrate": "<PERSON><PERSON><PERSON><PERSON>", "Exclude message from prompts": "Bericht uitsluiten van prompts", "Include message in prompts": "Bericht opnemen in prompts", "Embed file or image": "Bestand of afbeelding insluiten", "Create checkpoint": "<PERSON><PERSON><PERSON> maken", "Create Branch": "Vertakking maken", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Open checkpoint chat": "Open controlepostchat", "Edit": "Bewerken", "Confirm": "Bevestigen", "Copy this message": "Dit bericht kopiëren", "Delete this message": "Dit bericht verwijderen", "Move message up": "Bericht omhoog verplaatsen", "Move message down": "Bericht omlaag verplaatsen", "Enlarge": "<PERSON><PERSON><PERSON><PERSON>", "Welcome to SillyTavern!": "Welkom bij Silly<PERSON>avern!", "welcome_message_part_1": "<PERSON><PERSON>", "welcome_message_part_2": "Officiële documentatie", "welcome_message_part_3": null, "welcome_message_part_4": "Type", "welcome_message_part_5": "in de chat voor opdrachten en macro's.", "welcome_message_part_6": "Sluit je aan bij de", "Discord server": "Discord-server", "welcome_message_part_7": "voor info en aankondigingen.", "SillyTavern is aimed at advanced users.": "Si<PERSON><PERSON><PERSON><PERSON> is gericht op gevorderde gebruikers.", "If you're new to this, enable the simplified UI mode below.": "Als dit nieuw voor je is, schakel dan de vereenvoudigde UI-modus hieronder in.", "Change it later in the 'User Settings' panel.": "<PERSON><PERSON><PERSON><PERSON> het later in het paneel 'Gebruikersinstellingen'.", "Enable simple UI mode": "<PERSON><PERSON><PERSON> de eenvoudige UI-modus in", "Looking for AI characters?": "Op zoek naar AI-personages?", "onboarding_import": "Importeren", "from supported sources or view": "van ondersteunde bronnen of weergave", "Sample characters": "Voorbeeld karakters", "Your Persona": "Je <PERSON>age", "Before you get started, you must select a persona name.": "Voordat u aan de slag gaat, moet u een personanaam selecteren.", "welcome_message_part_8": "Dit kunt u op ieder moment wijzigen via de", "welcome_message_part_9": "icoon.", "Persona Name:": "Personanaam:", "Temporarily disable automatic replies from this character": "Tijdelijk automatische antwoorden van dit personage uitschakelen", "Enable automatic replies from this character": "Automatische antwoorden van dit personage inschakelen", "Trigger a message from this character": "<PERSON><PERSON> bericht activeren van dit personage", "Move up": "Omhoog verplaatsen", "Move down": "Omlaag verplaatsen", "View character card": "Bekijk personage<PERSON><PERSON>", "Remove from group": "Verwijderen uit groep", "Add to group": "Toevoegen aan groep", "Alternate Greetings": "Alternatieve groeten", "Alternate_Greetings_desc": "Deze worden weergegeven als veegbewegingen in het eerste bericht wanneer u een nieuwe chat start.\n                Groepsleden kunnen een van hen selecteren om het gesprek te starten.", "Alternate Greetings Hint": "<PERSON>lik op de knop om aan de slag te gaan!", "(This will be the first message from the character that starts every chat)": "(Dit zal het eerste bericht zijn van het personage dat elke chat begint)", "Forbid Media Override explanation": "Mogelijk<PERSON><PERSON> van het huidige personage/groep om externe media te gebruiken in chats.", "Forbid Media Override subtitle": "Media: a<PERSON><PERSON><PERSON><PERSON>, video's, audio. Extern: niet gehost op de lokale server.", "Always forbidden": "<PERSON><PERSON><PERSON><PERSON> verboden", "Always allowed": "<PERSON><PERSON><PERSON><PERSON>", "View contents": "Bekijk de inhoud", "Remove the file": "Verwijder het bestand", "Unique to this chat": "Uniek aan deze chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Controlepunten nemen de notitie over van hun ouder en kunnen daarna afzonderlijk worden gewijzigd.", "Include in World Info Scanning": "Opnemen in World Info Scannen", "Before Main Prompt / Story String": "Vóór de hoofdprompt / verha<PERSON><PERSON>ks", "After Main Prompt / Story String": "Na de hoofdprompt / verha<PERSON><PERSON>ks", "as": "als", "Insertion Frequency": "Inbrengfrequentie", "(0 = Disable, 1 = Always)": "(0 = Uitschakelen, 1 = Altijd)", "User inputs until next insertion:": "Gebruikersinvoer tot de volgende invoeging:", "Character Author's Note (Private)": "Notitie van de auteur van het personage (privé)", "Won't be shared with the character card on export.": "<PERSON>t bij het exporteren niet g<PERSON> met de ka<PERSON><PERSON>.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Wordt automatisch toegevoegd als auteursnotitie voor dit personage. Zal in groepen worden gebruikt, maar\n                            kan niet worden gewijzigd wanneer een groepschat geopend is.", "Use character author's note": "Gebruik de notitie van de auteur van het personage", "Replace Author's Note": "<PERSON><PERSON><PERSON><PERSON> de opmerking van de auteur", "Default Author's Note": "Standaardnotitie van de auteur", "Will be automatically added as the Author's Note for all new chats.": "Wordt automatisch toegevoegd als notitie van de auteur voor alle nieuwe chats.", "Chat CFG": "Chat CFG", "1 = disabled": "1 = uitgeschakeld", "write short replies, write replies using past tense": "schrijf korte antwoorden, schrijf antwoorden in de verleden tijd", "Positive Prompt": "Positieve prompt", "Use character CFG scales": "Gebruik karakter CFG-schalen", "Character CFG": "Karakter CFG", "Will be automatically added as the CFG for this character.": "Wordt automatisch toegevoegd als de CFG voor dit personage.", "Global CFG": "Mondiale CFG", "Will be used as the default CFG options for every chat unless overridden.": "Wordt gebruikt als de standaard CFG-opties voor elke chat, tenzij deze wordt overschreven.", "CFG Prompt Cascading": "CFG-promptcascadering", "Combine positive/negative prompts from other boxes.": "Combineer positieve/negatieve aanwijzingen uit andere vakken.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Als u bijvoorbeeld de v<PERSON> chat, globaal en teken aanvinkt, worden alle negatieve prompts samenge<PERSON><PERSON>d tot een door komma's gescheiden tekenreeks.", "Always Include": "Altijd opnemen", "Chat Negatives": "<PERSON><PERSON>-negat<PERSON><PERSON>", "Character Negatives": "Negatieven van het karakter", "Global Negatives": "Mondiale negatieven", "Custom Separator:": "Aangepaste scheidingsteken:", "Insertion Depth:": "Insteekdiepte:", "Token Probabilities": "Token-kansen", "Select a token to see alternatives considered by the AI.": "Selecteer een token om alternatieven te zien die door de AI worden overwogen.", "Not connected to API!": "<PERSON>et verbonden met API!", "Type a message, or /? for help": "<PERSON>p een bericht, of /? voor hulp", "Continue script execution": "<PERSON><PERSON> door met de u<PERSON><PERSON><PERSON> van het script", "Pause script execution": "<PERSON><PERSON><PERSON> van het script", "Abort script execution": "Uitvoering van script afbreken", "Abort request": "Verzoek annuleren", "Continue the last message": "Ga door met het laatste bericht", "Send a message": "<PERSON><PERSON> bericht versturen", "Close chat": "Chat sluiten", "Toggle Panels": "<PERSON><PERSON><PERSON> tussen panelen", "Back to parent chat": "Terug naar ouderlijke chat", "Save checkpoint": "<PERSON><PERSON><PERSON> op<PERSON>", "Convert to group": "Converteren naar groep", "Start new chat": "Nieuwe chat starten", "Manage chat files": "<PERSON><PERSON><PERSON>", "Delete messages": "Berichten verwijderen", "Regenerate": "Regenereren", "Ask AI to write your message for you": "Vraag de AI om je bericht voor je te schrijven", "Impersonate": "<PERSON><PERSON><PERSON>", "Continue": "Doorgaan", "Bind user name to that avatar": "Gebruikersnaam aan die avatar binden", "Change persona image": "<PERSON><PERSON><PERSON><PERSON> van persona wijzigen", "Select this as default persona for the new chats.": "Selecteer dit als standaardpersona voor de nieuwe chats.", "Delete persona": "Persona verwi<PERSON>en", "These characters are the winners of character design contests and have outstandable quality.": "Deze karakters zijn de winna<PERSON> van karakterontwerpwedstrijden en hebben een uitstekende kwaliteit.", "Contest Winners": "<PERSON><PERSON><PERSON>", "These characters are the finalists of character design contests and have remarkable quality.": "Deze karakters zijn de <PERSON>en van karakterontwerpwedstrijden en hebben een opmerkelijke kwaliteit.", "Featured Characters": "Uitgelichte personages", "Attach a File": "Voeg een bestand toe", "Open Data Bank": "Open databank", "Enter a URL or the ID of a Fandom wiki page to scrape:": "<PERSON><PERSON><PERSON> een URL of de ID in van een Fandom-wikipagina die u wilt schrapen:", "Examples:": "Voorbeelden:", "Example:": "Voorbeeld:", "Single file": "<PERSON><PERSON> bestand", "All articles will be concatenated into a single file.": "Alle artikelen worden samengevoegd in één bestand.", "File per article": "Bestand per artikel", "Each article will be saved as a separate file.": "<PERSON><PERSON> a<PERSON>. Elk artikel wordt als apart bestand opgeslagen.", "Data Bank": "Data bank", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Deze bestanden zijn besch<PERSON> voor extensies die bijlagen ondersteunen (bijvoorbeeld Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Ondersteunde bestandstypen: platte tekst, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Sleep bestanden hierheen om ze te uploaden.", "Date (Newest First)": "Datum (Nieuwste eerst)", "Date (Oldest First)": "Datum (oudste e<PERSON>t)", "Name (A-Z)": "<PERSON>am (A-Z)", "Name (Z-A)": "<PERSON><PERSON> (Z-A)", "Size (Smallest First)": "Grootte (kleinste e<PERSON>t)", "Size (Largest First)": "Grootte (grootste e<PERSON>t)", "Bulk Edit": "Bulkbewerking", "Select All": "Selecteer alles", "Select None": "Selecteer <PERSON><PERSON>", "Global Attachments": "Globale bijlagen", "These files are available for all characters in all chats.": "Deze bestanden zijn be<PERSON> voor alle personages in alle chats.", "Character Attachments": "Karakterbijlagen", "These files are available the current character in all chats they are in.": "Deze bestanden zijn be<PERSON> voor het huidige karakter in alle chats waarin ze zich bevinden.", "Saved locally. Not exported.": "Lokaal opgeslagen. Niet geëxporteerd.", "Chat Attachments": "Chatbijlagen", "These files are available to all characters in the current chat.": "Deze bestanden zijn be<PERSON> voor alle personages in de huidige chat.", "Enter a base URL of the MediaWiki to scrape.": "Voer een basis-URL in van de MediaWiki die u wilt schrapen.", "Don't include the page name!": "<PERSON>er<PERSON>d de paginanaam niet!", "Enter web URLs to scrape (one per line):": "<PERSON><PERSON><PERSON> web-URL's in om te schrapen (één per regel):", "Enter a video URL to download its transcript.": "<PERSON><PERSON><PERSON> een video-URL of ID in om het transcript ervan te downloaden.", "Expression API": "Lokaal\nExtra's\nLLM", "ext_sum_with": "<PERSON><PERSON><PERSON><PERSON> met:", "ext_sum_main_api": "Hoofd-API", "ext_sum_current_summary": "<PERSON><PERSON>g ​​overzicht:", "ext_sum_restore_previous": "Herstel vorige", "ext_sum_memory_placeholder": "Hier wordt een samenvatting gegenereerd...", "Trigger a summary update right now.": "Vat nu samen", "ext_sum_force_text": "Vat nu samen", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Schakel automatische samenvattingsupdates uit. Tijdens de pauze blijft de samenvatting zoals ze is. Je kunt nog steeds een update forceren door op de knop Nu samenvatten te drukken (die alleen beschik<PERSON>ar is met de hoofd-API).", "ext_sum_pause": "<PERSON><PERSON>", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Laat wereldinformatie en de notitie van de auteur weg uit de samen te vatten tekst. Heeft alleen effect bij gebruik van de Main API. De Extras API laat WI/AN altijd weg.", "ext_sum_no_wi_an": "<PERSON>n WI/AN", "ext_sum_settings_tip": "Bewerk de samenvattingsprompt, invoegpositie, enz.", "ext_sum_settings": "Samenvattingsinstellingen", "ext_sum_prompt_builder": "<PERSON><PERSON><PERSON> bouwer", "ext_sum_prompt_builder_1_desc": "De extensie bouwt zijn eigen prompt op met behul<PERSON> van berichten die nog niet zijn samengevat. Blokkeert de chat totdat de samenvatting is gegenereerd.", "ext_sum_prompt_builder_1": "<PERSON><PERSON><PERSON>, blokkerend", "ext_sum_prompt_builder_2_desc": "De extensie bouwt zijn eigen prompt op met behul<PERSON> van berichten die nog niet zijn samengevat. Blokkeert de chat niet terwijl de samenvatting wordt gegenereerd. Niet alle backends ondersteunen deze modus.", "ext_sum_prompt_builder_2": "<PERSON><PERSON><PERSON>, niet-blokkerend", "ext_sum_prompt_builder_3_desc": "De extensie gebruikt de reguliere hoofdpromptbouwer en voegt het samenvattingsverzoek eraan toe als het laatste systeembericht.", "ext_sum_prompt_builder_3": "Klassiek, blokkerend", "Summary Prompt": "<PERSON>nvatting Prompt", "ext_sum_restore_default_prompt_tip": "Standaardprompt herstellen", "ext_sum_prompt_placeholder": "Deze prompt wordt naar AI gestuurd om het genereren van een samenvatting aan te vragen. {{words}} wordt omgezet naar de parameter 'Aantal woorden'.", "ext_sum_target_length_1": "Doelsamenvattingslengte", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "woorden)", "ext_sum_api_response_length_1": "<PERSON><PERSON><PERSON> van de API-reactie", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "Munten)", "ext_sum_0_default": "0 = standaard", "ext_sum_raw_max_msg": "[Raw] Max. aantal berichten per verzoek", "ext_sum_0_unlimited": "0 = onbeperkt", "Update frequency": "Frequentie bijwerken", "ext_sum_update_every_messages_1": "Elke update", "ext_sum_update_every_messages_2": "<PERSON><PERSON><PERSON>", "ext_sum_0_disable": "0 = uitschakelen", "ext_sum_auto_adjust_desc": "Probeer het interval automatisch aan te passen op basis van de chatstatistieken.", "ext_sum_update_every_words_1": "Update elke", "ext_sum_update_every_words_2": "woorden", "ext_sum_both_sliders": "Als beide schuifregelaars niet nul zijn, <PERSON><PERSON> beide met hun respectievelijke intervallen samenvattingsupdates.", "ext_sum_injection_template": "Injectiesjabloon", "ext_sum_memory_template_placeholder": "{{summary}} wordt omgezet naar de huidige samenvattingsinhoud.", "ext_sum_injection_position": "Injectiepositie", "How many messages before the current end of the chat.": "<PERSON><PERSON>el berichten vóór het huidige einde van de chat.", "ext_regex_title": "Regex", "ext_regex_new_global_script": "+ Globaal", "ext_regex_new_scoped_script": "+ <PERSON><PERSON><PERSON>", "ext_regex_import_script": "Importeren", "ext_regex_global_scripts": "Globale scripts", "ext_regex_global_scripts_desc": "Beschikbaar voor alle karakters. Opgeslagen in lokale instellingen.", "ext_regex_scoped_scripts": "Scoped-scripts", "ext_regex_scoped_scripts_desc": "<PERSON>een besch<PERSON> voor dit personage. Opgeslagen in de kaartgegevens.", "Regex Editor": "Regex-editor", "Test Mode": "Testmodus", "ext_regex_desc": "Regex is een hulpmiddel om tekenreeksen te vinden/vervangen met behulp van reguliere expressies. Wilt u meer weten, klik dan op de ? naast de titel.", "Input": "Invoer", "ext_regex_test_input_placeholder": "Typ hier...", "Output": "Uit<PERSON><PERSON>", "ext_regex_output_placeholder": "<PERSON><PERSON>", "Script Name": "Scriptnaam", "Find Regex": "Zoek Regex", "Replace With": "Vervangen door", "ext_regex_replace_string_placeholder": "Gebruik {{match}} om de overeenkomende tekst uit de Find Regex of $1, $2, enz. op te nemen voor capture-groepen.", "Trim Out": "Uitsnijden", "ext_regex_trim_placeholder": "Verwijdert globaal alle ongewenste onderdelen uit een regex-match voordat deze worden vervangen. Scheid elk element door een enter.", "ext_regex_affects": "Be<PERSON>nv<PERSON><PERSON><PERSON>", "ext_regex_user_input": "Gebruikers invoer", "ext_regex_ai_output": "AI-uitvoer", "Slash Commands": "Slash-opdrachten", "ext_regex_min_depth_desc": "Wanneer toegepast op aanwi<PERSON>zing<PERSON> of weergave, heeft dit alleen invloed op berichten die ten minste N niveaus diep zijn. 0 = laatste bericht, 1 = voorlaatste bericht, etc. Telt alleen WI-invoer @Depth en bruikbare berichten, d.w.z. niet verborgen of systeem.", "Min Depth": "Minimale diepte", "ext_regex_min_depth_placeholder": "Onbeperkt", "ext_regex_max_depth_desc": "Wanneer toegepast op aanwijzingen of weergave, heeft dit alleen invloed op berichten die niet dieper zijn dan N niveaus. 0 = laatste bericht, 1 = voorlaatste bericht, etc. Telt alleen WI-invoer @Depth en bruikbare berichten, d.w.z. niet verborgen of systeem.", "ext_regex_other_options": "Andere opties", "Only Format Display": "Alleen formaatweergave", "ext_regex_only_format_prompt_desc": "De chatgeschiedenis verandert niet, alleen de prompt waarop het verzoek wordt verzonden (bij het genereren).", "Only Format Prompt (?)": "Alleen formatteringsprompt", "Run On Edit": "Uitvoeren op Bewerken", "ext_regex_substitute_regex_desc": "Vervang {{macros}} in Find Regex voordat u het uitvoert", "Substitute Regex": "Vervang Regex", "ext_regex_import_target": "Importeren naar:", "ext_regex_disable_script": "Script uitschakelen", "ext_regex_enable_script": "Script inschakelen", "ext_regex_edit_script": "Script bewerken", "ext_regex_move_to_global": "Ga naar globale scripts", "ext_regex_move_to_scoped": "Overstappen op scripts met een bepaald bereik", "ext_regex_export_script": "Script exporteren", "ext_regex_delete_script": "Script verwijderen", "Trigger Stable Diffusion": "Trigger stabiele diffusie", "sd_Yourself": "<PERSON><PERSON><PERSON>", "sd_Your_Face": "<PERSON> gezicht", "sd_Me": "<PERSON><PERSON>", "sd_The_Whole_Story": "Het hele verhaal", "sd_The_Last_Message": "Het laatste bericht", "sd_Raw_Last_Message": "<PERSON><PERSON><PERSON> la<PERSON> be<PERSON>t", "sd_Background": "Achtergrond", "Image Generation": "<PERSON><PERSON> gene<PERSON>", "sd_refine_mode": "Sta toe dat prompts handmatig worden bewerkt voordat ze naar de generatie-API worden verzonden", "sd_refine_mode_txt": "Bewerk aanwijzingen vóór het genereren", "sd_interactive_mode": "Genereer automatisch afbeeldingen bij het verzenden van berichten zoals 'stuur mij een foto van de kat'.", "sd_interactive_mode_txt": "Interactieve modus", "sd_multimodal_captioning": "Gebruik multimodale ondertiteling om aanwijzingen te genereren voor gebruikers- en karakterportretten op basis van hun avatars.", "sd_multimodal_captioning_txt": "Gebruik multimodale ondertiteling voor portretten", "sd_expand": "<PERSON><PERSON><PERSON> prompts automat<PERSON> uit met behulp van het tekstgeneratiemodel", "sd_expand_txt": "Automatisch verbeteren van prompts", "sd_snap": "<PERSON><PERSON> met een gef<PERSON><PERSON><PERSON><PERSON> beeld<PERSON>hou<PERSON> (portretten, achtergronden) tot de dichtstbijzijnde bekende resolutie, terwijl u probeert het absolute aantal pixels te behouden (aanbevolen voor SDXL).", "sd_snap_txt": "Maak automatisch aangepaste resoluties", "Source": "<PERSON><PERSON>", "sd_auto_url": "Voorbeeld: {{auto_url}}", "Authentication (optional)": "Authenticatie (optioneel)", "Example: username:password": "Voorbeeld: gebruikersnaam:wachtwoord", "Important:": "Belangrijk:", "sd_auto_auth_warning_1": "voer SD Web UI uit met de", "sd_auto_auth_warning_2": "vlag! De server moet toegankelijk zijn vanaf de SillyTavern-hostmachine.", "sd_drawthings_url": "Voorbeeld: {{drawthings_url}}", "sd_drawthings_auth_txt": "voer de DrawThings-app uit met HTTP API-switch ingeschakeld in de gebruikersinterface! De server moet toegankelijk zijn vanaf de SillyTavern-hostmachine.", "sd_vlad_url": "Voorbeeld: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "De server moet toegankelijk zijn van<PERSON> SillyTavern-hostmachine.", "Hint: Save an API key in AI Horde API settings to use it here.": "Tip: sla een API-sleutel op in de AI Horde API-instellingen om deze hier te gebruiken.", "Allow NSFW images from Horde": "Sta NSFW-<PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON> toe", "Sanitize prompts (recommended)": "Ontsmettingsmeldingen (aanbevolen)", "Automatically adjust generation parameters to ensure free image generations.": "Pas de generatieparameters automatisch aan om vrije beeldgeneraties te garanderen.", "Avoid spending Anlas": "Vermijd het uit<PERSON><PERSON>", "Opus tier": "(Opus-niveau)", "View my Anlas": "Bekijk mijn <PERSON>'s", "These settings only apply to DALL-E 3": "Deze instellingen zijn alleen van toepassing op DALL-E 3", "Image Style": "Afbeeldingsstijl", "Image Quality": "Beeldkwaliteit", "Standard": "Standaard", "HD": "HD", "sd_comfy_url": "Voorbeeld: {{comfy_url}}", "Open workflow editor": "Open de werkstroomeditor", "Create new workflow": "<PERSON><PERSON><PERSON><PERSON> een nieuwe werkstroom", "Delete workflow": "Workflow verwijderen", "Enhance": "Uitbreiden", "Refine": "Verfijnen", "Decrisper": "Ontscherper", "Sampling steps": "Bemonsteringsstappen ()", "Width": "Breedte ()", "Height": "<PERSON><PERSON><PERSON> ()", "Resolution": "Oplossing", "Model": "Model", "Sampling method": "Bemonsteringsmethode", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (niet alle samplers worden ondersteund)", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA-versies van samplers zijn aangepast om beter te presteren bij hoge resolutie.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "DYN-varianten van SMEA-samplers leiden vaak tot een gevarieerdere output, maar kunnen mislukken bij zeer hoge resoluties.", "DYN": "DYN", "Scheduler": "Planner", "Restore Faces": "Gezichten herstellen", "Hires. Fix": "<PERSON><PERSON> in. Repareren", "Upscaler": "Upscaler", "Upscale by": "Opschalen door", "Denoising strength": "Denoiserende kracht", "Hires steps (2nd pass)": "<PERSON><PERSON> (2e pas)", "Preset for prompt prefix and negative prompt": "Voorinstelling voor promptvoorvoegsel en negatieve prompt", "Style": "<PERSON><PERSON><PERSON><PERSON>", "Save style": "<PERSON><PERSON><PERSON><PERSON>", "Delete style": "Stijl verwijderen", "Common prompt prefix": "Algemeen promptvoor<PERSON>egsel", "sd_prompt_prefix_placeholder": "Gebruik {prompt} om op te geven waar de gegenereerde prompt moet worden ingevoegd", "Negative common prompt prefix": "Negatief gemeenschappelijk promptvoorvoegsel", "Character-specific prompt prefix": "Tekenspecifiek promptvoorvoegsel", "Won't be used in groups.": "<PERSON>t niet in groepen gebruikt.", "sd_character_prompt_placeholder": "Alle kenmerken die het momenteel geselecteerde teken beschrijven. Wordt toegevoegd na een algemeen promptvoorvoegsel.\nVoorbeeld: vrouw, gro<PERSON> ogen, bruin haar, roze shirt", "Character-specific negative prompt prefix": "Tekenspecifiek negatief promptvoorvoegsel", "sd_character_negative_prompt_placeholder": "Alle kenmerken die niet mogen verschijnen voor het geselecteerde teken. Wordt toegevoegd na een negatief algemeen promptvoorvoegsel.\nVoorbeeld: si<PERSON><PERSON>, scho<PERSON><PERSON>, brillen", "Shareable": "<PERSON><PERSON><PERSON><PERSON>", "Image Prompt Templates": "Sjablonen voor beeldprompts", "Vectors Model Warning": "Het wordt aanbevolen om vectoren te verwijderen wanneer u tijdens de chat van model verandert. <PERSON> leidt dit tot ondermaatse resultaten.", "Translate files into English before processing": "Vertaal bestanden naar het Engels voordat ze worden verwerkt", "Manager Users": "Gebruike<PERSON> beheren", "New User": "<PERSON>eu<PERSON> gebruiker", "Status:": "Toestand:", "Created:": "Gemaakt:", "Display Name:": "Weergavenaam:", "User Handle:": "Gebruikershandvat:", "Password:": "Wachtwoord:", "Confirm Password:": "Bevestig wachtwoord:", "This will create a new subfolder...": "<PERSON>erd<PERSON> wordt een nieuwe submap gemaakt in de map /data/ met de gebruikersnaam als mapnaam.", "Current Password:": "<PERSON><PERSON><PERSON> ​​wachtwoord:", "New Password:": "Nieuw paswoord:", "Confirm New Password:": "Bevestig nieuw wachtwoord:", "Debug Warning": "Functies in deze categorie zijn alleen voor gevorderde gebruikers. Klik nergens op als u niet zeker bent van de gevolgen.", "Execute": "Uitvoeren", "Are you sure you want to delete this user?": "Weet u zeker dat u deze gebruiker wilt verwijderen?", "Deleting:": "Verwijderen:", "Also wipe user data.": "Wis ook gebruikersgegevens.", "Warning:": "Waarschuwing:", "This action is irreversible.": "<PERSON><PERSON> actie is on<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Type the user's handle below to confirm:": "<PERSON><PERSON> hieronder de gebruikers<PERSON><PERSON> van de gebruiker om te bevestigen:", "Import Characters": "Tekens importeren", "Enter the URL of the content to import": "<PERSON><PERSON><PERSON> de URL in van de inhoud die u wilt importeren", "Supported sources:": "Ondersteunde bronnen:", "char_import_1": "Kopvoorn-karakter (directe link of ID)", "char_import_example": "Voorbeeld:", "char_import_2": "Chub Lorebook (directe link of ID)", "char_import_3": "JanitorAI-personage (directe link of UUID)", "char_import_4": "Pygmalion.chat-teken (directe link of UUID)", "char_import_5": "AICharacterCards.com-teken (directe link of ID)", "char_import_6": "Directe PNG-link (zie", "char_import_7": "voor toegestane hosts)", "char_import_8": "R<PERSON>uRealm-personage (directe link)", "char_import_9": "<PERSON><PERSON><PERSON>-personage (directe link)", "char_import_10": "Perchance-personage (directe link of UUID + .gz)", "Supports importing multiple characters.": "Ondersteunt het importeren van meerdere tekens.", "Write each URL or ID into a new line.": "<PERSON><PERSON><PERSON><PERSON><PERSON> elke URL of ID op een nieuwe regel.", "Export for character": "Exporteren voor karakter", "Export prompts for this character, including their order.": "Exporteer prompts voor dit personage, inclusief hun volgorde.", "Export all": "Alles exporteren", "Export all your prompts to a file": "Exporteer al uw prompts naar een bestand", "Insert prompt": "Prompt invoegen", "Delete prompt": "Prompt verwijderen", "Import a prompt list": "<PERSON><PERSON> promptlijst importeren", "Export this prompt list": "Deze promptlijst exporteren", "Reset current character": "<PERSON><PERSON><PERSON> karakter resetten", "New prompt": "<PERSON><PERSON><PERSON> prompt", "Prompts": "Prompts", "Total Tokens:": "Totale Tokens:", "prompt_manager_tokens": "Munten", "Are you sure you want to reset your settings to factory defaults?": "Weet u zeker dat u uw instellingen wilt terugzetten naar de fabrieksinstellingen?", "Don't forget to save a snapshot of your settings before proceeding.": "Vergeet niet een momentopname van uw instellingen op te slaan voordat u doorgaat.", "Settings Snapshots": "Instellingen Momentopnamen", "Record a snapshot of your current settings.": "Maak een momentopname van uw huidige instellingen.", "Make a Snapshot": "Maak een momentop<PERSON>", "Restore this snapshot": "<PERSON><PERSON><PERSON> deze momentopname", "Hi,": "Hoi,", "To enable multi-account features, restart the SillyTavern server with": "Om functies voor meerdere accounts in te schakelen, start u de SillyTavern-server opnieuw op met", "set to true in the config.yaml file.": "ingesteld op true in het bestand config.yaml.", "Account Info": "Account informatie", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Om uw gebruikersavatar te wijzigen, gebruikt u de ondersta<PERSON>e knoppen of selecteert u een standaardpersona in het menu Personabeheer.", "Set your custom avatar.": "<PERSON><PERSON> uw aangepaste avatar in.", "Remove your custom avatar.": "<PERSON><PERSON><PERSON><PERSON><PERSON> uw aangepaste avatar.", "Handle:": "Hendel:", "This account is password protected.": "Dit account is beveiligd met een wachtwoord.", "This account is not password protected.": "Dit account is niet beveiligd met een wachtwoord.", "Account Actions": "Accountacties", "Change Password": "<PERSON><PERSON> wa<PERSON>", "Manage your settings snapshots.": "<PERSON><PERSON><PERSON> uw moment<PERSON><PERSON><PERSON> van instellingen.", "Download a complete backup of your user data.": "Download een volledige back-up van uw gebruikersgegevens.", "Download Backup": "Back-up downloaden", "Danger Zone": "Gevarenzone", "Reset your settings to factory defaults.": "Reset uw instellingen naar de fabrieksinstellingen.", "Reset Settings": "Reset instellingen", "Wipe all user data and reset your account to factory settings.": "Wis alle gebruikersgegevens en reset uw account naar de fabrieksinstellingen.", "Reset Everything": "Alles opnieuw instellen", "Reset Code:": "Reset code:", "Want to update?": "Wil je SillyTavern updaten?", "How to start chatting?": "Hoe begin je met chatten?", "Click _space": "Klik", "and select a": " en selecteer een", "Chat API": " Chat-API", "and pick a character.": "en kies een personage.", "You can browse a list of bundled characters in the": "U kunt door een lij<PERSON> met gebundelde karakters bladeren in de", "Download Extensions & Assets": "Extensies en middelen downloaden", "menu within": "menu binnen", "Confused or lost?": "In de war of verdwaald?", "click these icons!": "klik op deze pictogrammen!", "in the chat bar": " in de chatbalk", "SillyTavern Documentation Site": "SillyTavern Documentatiesite", "Extras Installation Guide": "Extra Installatiegids", "Still have questions?": "Heb je nog vragen?", "Join the SillyTavern Discord": "Word lid van de SillyTavern Discord", "Post a GitHub issue": "Plaats een GitHub-probleem", "Contact the developers": "Neem contact op met de ontwikkelaars"}