<div id="qr--modalEditor">
	<div id="qr--main">
		<h3 data-i18n="Labels and Message">Labels and Message</h3>
		<div class="qr--labels">
			<label class="qr--fit">
				<span class="qr--labelText" data-i18n="Label">Icon</span>
				<small class="qr--labelHint">&nbsp;</small>
				<div class="menu_button fa-fw" id="qr--modal-icon" title="Click to change icon"></div>
			</label>
			<div class="label">
				<span class="qr--labelText" data-i18n="Label">Label</span>
				<small class="qr--labelHint" data-i18n="(label of the button, if no icon is chosen) ">(label of the button, if no icon is chosen)</small>
				<div class="qr--inputGroup">
					<label class="checkbox_label" title="Show label even if an icon is assigned">
						<input type="checkbox" id="qr--modal-showLabel">
						Show
					</label>
					<input type="text" class="text_pole" id="qr--modal-label">
					<div class="menu_button fa-fw fa-solid fa-chevron-down" id="qr--modal-switcher" title="Switch to another QR"></div>
				</div>
			</div>
			<label>
				<span class="qr--labelText" data-i18n="Title">Title</span>
				<small class="qr--labelHint" data-i18n="(tooltip, leave empty to show message or /command)">(tooltip, leave empty to show message or /command)</small>
				<input type="text" class="text_pole" id="qr--modal-title">
			</label>
		</div>
		<div class="qr--modal-messageContainer">
			<label for="qr--modal-message" data-i18n="Message / Command:">
				Message / Command:
			</label>
			<div class="qr--modal-editorSettings">
				<label class="checkbox_label">
					<input type="checkbox" id="qr--modal-wrap">
					<span data-i18n="Word wrap">Word wrap</span>
				</label>
				<label class="checkbox_label">
					<span data-i18n="Tab size:">Tab size:</span>
					<input type="number" min="1" max="9" id="qr--modal-tabSize" class="text_pole">
				</label>
				<label class="checkbox_label">
					<input type="checkbox" id="qr--modal-executeShortcut">
					<span data-i18n="Ctrl+Enter to execute">Ctrl+Enter to execute</span>
				</label>
				<label class="checkbox_label">
					<input type="checkbox" id="qr--modal-syntax">
					<span>Syntax highlight</span>
				</label>
				<small>Ctrl+Alt+Click (or F9) to set / remove breakpoints</small>
				<small>Ctrl+<span id="qr--modal-commentKey"></span> to toggle block comments</small>
			</div>
			<div id="qr--modal-messageHolder">
				<pre id="qr--modal-messageSyntax"><code id="qr--modal-messageSyntaxInner" class="hljs language-stscript"></code></pre>
				<textarea id="qr--modal-message" spellcheck="false"></textarea>
			</div>
		</div>
	</div>



	<div id="qr--resizeHandle"></div>



	<div id="qr--qrOptions">
		<h3 data-i18n="Context Menu">Context Menu</h3>
		<div id="qr--ctxEditor">
			<template id="qr--ctxItem">
				<div class="qr--ctxItem" data-order="0">
					<div class="drag-handle ui-sortable-handle">☰</div>
					<select class="qr--set"></select>
					<label class="qr--isChainedLabel checkbox_label" title="When enabled, the current Quick Reply will be sent together with (before) the clicked QR from the context menu.">
						<span data-i18n="Chaining:">Chaining:</span>
						<input type="checkbox" class="qr--isChained">
					</label>
					<div class="qr--delete menu_button menu_button_icon fa-solid fa-trash-can" title="Remove entry"></div>
				</div>
			</template>
		</div>
		<div class="qr--ctxEditorActions">
			<span id="qr--ctxAdd" class="menu_button menu_button_icon fa-solid fa-plus" title="Add quick reply set to context menu"></span>
		</div>


		<h3 data-i18n="Auto-Execute">Auto-Execute</h3>
		<div id="qr--autoExec" class="flex-container flexFlowColumn">
			<label class="checkbox_label" title="Prevent this quick reply from triggering other auto-executed quick replies while auto-executing (i.e., prevent recursive auto-execution)">
				<input type="checkbox" id="qr--preventAutoExecute" >
				<span><i class="fa-solid fa-fw fa-plane-slash"></i><span data-i18n="Don't trigger auto-execute">Don't trigger auto-execute</span></span>
			</label>
			<label class="checkbox_label">
				<input type="checkbox" id="qr--isHidden" >
				<span><i class="fa-solid fa-fw fa-eye-slash"></i><span data-i18n="Invisible (auto-execute only)">Invisible (auto-execute only)</span></span>
			</label>
			<label class="checkbox_label">
				<input type="checkbox" id="qr--executeOnStartup" >
				<span><i class="fa-solid fa-fw fa-rocket"></i><span data-i18n="Execute on startup">Execute on startup</span></span>
			</label>
			<label class="checkbox_label">
				<input type="checkbox" id="qr--executeOnUser" >
				<span><i class="fa-solid fa-fw fa-user"></i><span data-i18n="Execute on user message">Execute on user message</span></span>
			</label>
			<label class="checkbox_label">
				<input type="checkbox" id="qr--executeOnAi" >
				<span><i class="fa-solid fa-fw fa-robot"></i><span data-i18n="Execute on AI message">Execute on AI message</span></span>
			</label>
			<label class="checkbox_label">
				<input type="checkbox" id="qr--executeOnChatChange" >
				<span><i class="fa-solid fa-fw fa-message"></i><span data-i18n="Execute on chat change">Execute on chat change</span></span>
			</label>
            <label class="checkbox_label">
                <input type="checkbox" id="qr--executeOnNewChat">
                <span><i class="fa-solid fa-fw fa-comments"></i><span data-i18n="Execute on new chat">Execute on new chat</span></span>
            </label>
            <label class="checkbox_label">
                <input type="checkbox" id="qr--executeOnGroupMemberDraft">
                <span><i class="fa-solid fa-fw fa-people-group"></i><span data-i18n="Execute on group member draft">Execute on group member draft</span></span>
            </label>
            <div class="flex-container alignItemsBaseline flexFlowColumn flexNoGap" title="Activate this quick reply when a World Info entry with the same Automation ID is triggered.">
                <small data-i18n="Automation ID:">Automation ID</small>
                <input type="text" id="qr--automationId" class="text_pole flex1" placeholder="( None )">
            </div>
		</div>


		<h3 data-i18n="Testing">Testing</h3>
		<div id="qr--modal-executeButtons">
			<div id="qr--modal-execute" class="qr--modal-executeButton menu_button" title="Execute the quick reply now">
				<i class="fa-solid fa-play"></i>
				<span data-i18n="Execute">Execute</span>
			</div>
			<div id="qr--modal-pause" class="qr--modal-executeButton menu_button" title="Pause / continue execution">
				<span class="qr--modal-executeComboIcon">
					<i class="fa-solid fa-play"></i>
					<i class="fa-solid fa-pause"></i>
				</span>
			</div>
			<div id="qr--modal-stop" class="qr--modal-executeButton menu_button" title="Abort execution">
				<i class="fa-solid fa-stop"></i>
			</div>
		</div>
		<div id="qr--modal-executeProgress"></div>
		<div id="qr--modal-executeErrors"></div>
		<div id="qr--modal-executeResult"></div>

		<div id="qr--modal-debugButtons">
			<div title="Resume" id="qr--modal-resume" class="qr--modal-debugButton menu_button"></div>
			<div title="Step Over" id="qr--modal-step" class="qr--modal-debugButton menu_button"></div>
			<div title="Step Into" id="qr--modal-stepInto" class="qr--modal-debugButton menu_button"></div>
			<div title="Step Out" id="qr--modal-stepOut" class="qr--modal-debugButton menu_button"></div>
			<div title="Minimize" id="qr--modal-minimize" class="qr--modal-debugButton menu_button fa-solid fa-minimize"></div>
			<div title="Maximize" id="qr--modal-maximize" class="qr--modal-debugButton menu_button fa-solid fa-maximize"></div>
		</div>
		<textarea rows="1" id="qr--modal-send_textarea" placeholder="Chat input for use with {{input}}" title="Chat input for use with {{input}}"></textarea>
		<div id="qr--modal-debugState"></div>
	</div>
</div>
