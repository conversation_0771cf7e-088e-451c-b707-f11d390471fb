# Requirements Document

## Introduction

本项目旨在开发新一代AI小说IDE (NovelCraft AI Studio)，这是一个集成了先进AI技术、知识图谱和智能改编功能的专业小说创作工具。该系统将提供Cursor风格的现代化界面，整合SillyTavern酒馆系统、Graphiti知识图谱、2.txt工具的成熟功能，以及AI工作流引擎，为小说创作者提供全方位的智能创作支持。

## Requirements

### Requirement 1: 现代化IDE界面

**User Story:** 作为小说创作者，我希望有一个现代化、专业的IDE界面，以便我能够高效地进行小说创作和管理。

#### Acceptance Criteria

1. WHEN 用户启动应用 THEN 系统应显示Cursor风格的三栏布局界面
2. WHEN 用户调整面板大小 THEN 系统应实时响应并保存布局设置
3. WHEN 用户切换主题 THEN 系统应立即应用新主题到所有界面组件
4. WHEN 用户使用快捷键 THEN 系统应响应相应的功能操作
5. WHEN 用户在不同设备上使用 THEN 界面应自适应屏幕尺寸
###
 Requirement 2: Monaco Editor集成

**User Story:** 作为小说创作者，我希望有一个功能强大的编辑器，以便我能够舒适地编写和编辑小说内容。

#### Acceptance Criteria

1. WHEN 用户在编辑器中输入文本 THEN 系统应提供语法高亮和智能提示
2. WHEN 用户选择文本 THEN 系统应自动提取上下文并传递给AI聊天面板
3. WHEN 用户按下Tab键 THEN 系统应接受AI建议并插入到编辑器中
4. WHEN 用户按下Esc键 THEN 系统应拒绝AI建议并清除预览
5. WHEN 用户使用快捷键 THEN 系统应触发相应的AI功能（续写、润色、扩写等）

### Requirement 3: 全局酒馆控制系统

**User Story:** 作为小说创作者，我希望能够管理AI角色、世界书和预设参数，以便我能够获得个性化和一致的AI创作支持。

#### Acceptance Criteria

1. WHEN 用户导入SillyTavern角色卡 THEN 系统应正确解析PNG格式的角色数据
2. WHEN 用户激活世界书条目 THEN 系统应根据关键词匹配和逻辑判断自动激活相关条目
3. WHEN 用户切换AI预设 THEN 系统应应用相应的采样参数和提示词模板
4. WHEN 用户与AI对话 THEN 系统应根据当前角色、世界书和预设生成响应
5. WHEN 用户修改角色设定 THEN 系统应实时更新AI行为和响应风格### R
equirement 4: Graphiti知识图谱集成

**User Story:** 作为小说创作者，我希望系统能够自动构建和维护小说的知识图谱，以便我能够获得智能的内容分析和创作建议。

#### Acceptance Criteria

1. WHEN 用户添加章节内容 THEN 系统应自动提取实体和关系并更新知识图谱
2. WHEN 用户查询角色关系 THEN 系统应基于图谱提供准确的关系网络信息
3. WHEN 用户请求情节分析 THEN 系统应利用图谱检测情节漏洞和一致性问题
4. WHEN 用户需要创作建议 THEN 系统应基于图谱推理提供智能的情节发展建议
5. WHEN 用户浏览知识图谱 THEN 系统应提供可视化的图谱浏览和交互界面

### Requirement 5: 2.txt功能整合与批量处理

**User Story:** 作为小说创作者，我希望能够批量处理多个章节并自动生成PSKB知识库，以便我能够高效地进行大规模的小说改编和增强。

#### Acceptance Criteria

1. WHEN 用户启动批量处理 THEN 系统应支持三种处理模式（严格串行、并行处理、无PSKB纯粹加料）
2. WHEN 系统生成PSKB THEN 应使用分块并行分析算法并由AI战略规划师整合
3. WHEN 处理过程中出现错误 THEN 系统应使用指数退避算法和安全检测进行智能重试
4. WHEN 用户需要更新PSKB THEN 系统应自动提取PSKB_UPDATE标记并智能维护知识库
5. WHEN 用户选择不同提示词方案 THEN 系统应应用相应的处理策略（侵略性加料、忠实执行PSKB等）#
## Requirement 6: WKB/EKB知识库系统

**User Story:** 作为小说创作者，我希望系统能够自动分析和维护作品的风格指南、大纲和事件知识库，以便我能够保持创作的一致性和质量。

#### Acceptance Criteria

1. WHEN 系统分析文本 THEN 应生成风格指南、核心戒律、作者大纲等WKB组件
2. WHEN 系统处理章节 THEN 应并行运行WKB分析师、评论家、总设计师和EKB事件分析师
3. WHEN 系统分块处理大文件 THEN 应按200KB/块进行智能分块并保持章节边界完整
4. WHEN 系统生成分析结果 THEN 应将结果自动映射到Graphiti知识图谱实体
5. WHEN 系统检测到内容冲突 THEN 应提供智能冲突检测和解决建议

### Requirement 7: AI智能改编引擎

**User Story:** 作为小说创作者，我希望系统能够自动识别和处理小说中的雷点，并提供智能的改编建议，以便我能够创作出符合目标读者喜好的作品。

#### Acceptance Criteria

1. WHEN 系统扫描章节内容 THEN 应自动检测一级雷点（死女、送女、绿帽、背叛、万人骑）
2. WHEN 系统发现雷点 THEN 应提供相应的改编方案库（假死脱险、坚决拒绝、误会澄清等）
3. WHEN 系统执行改编 THEN 应进行三阶段处理（雷点识别→情节重构→质量控制）
4. WHEN 系统完成改编 THEN 应验证内容一致性、角色性格保持和情节逻辑合理性
5. WHEN 用户需要自定义改编 THEN 系统应支持手动雷点标记和改编方案选择#
## Requirement 8: AI工作流引擎

**User Story:** 作为小说创作者，我希望能够设计和执行自定义的AI处理工作流，以便我能够自动化复杂的创作任务。

#### Acceptance Criteria

1. WHEN 用户设计工作流 THEN 系统应提供拖拽式的可视化工作流设计器
2. WHEN 用户配置节点 THEN 系统应提供丰富的预置处理节点（文本处理、AI处理、逻辑控制）
3. WHEN 用户执行工作流 THEN 系统应支持异步任务调度和并行执行优化
4. WHEN 工作流执行出错 THEN 系统应提供错误恢复机制和详细的调试信息
5. WHEN 用户需要扩展功能 THEN 系统应支持自定义节点开发和节点市场分享

### Requirement 9: 智能知识库与语义搜索

**User Story:** 作为小说创作者，我希望系统能够智能管理我的创作资料并提供高效的搜索功能，以便我能够快速找到相关信息。

#### Acceptance Criteria

1. WHEN 用户上传文档 THEN 系统应自动进行智能分块和语义嵌入
2. WHEN 用户搜索内容 THEN 系统应提供混合检索（向量相似度+关键词匹配+图谱遍历）
3. WHEN 系统生成内容 THEN 应使用RAG增强生成确保内容一致性
4. WHEN 用户查看搜索结果 THEN 系统应提供相关性排序和上下文预览
5. WHEN 用户需要相关推荐 THEN 系统应基于知识图谱提供智能内容推荐### Re
quirement 10: 系统性能与稳定性

**User Story:** 作为小说创作者，我希望系统能够稳定高效地运行，以便我能够专注于创作而不被技术问题干扰。

#### Acceptance Criteria

1. WHEN 用户启动应用 THEN 系统应在3秒内完成启动
2. WHEN 用户调用API THEN 系统应在500ms内响应
3. WHEN 系统运行 THEN 应保持99.5%以上的可用性
4. WHEN 用户激活世界书 THEN 系统应在200ms内完成激活响应
5. WHEN 用户进行语义搜索 THEN 系统应在1秒内返回搜索结果

### Requirement 11: 数据安全与备份

**User Story:** 作为小说创作者，我希望我的创作数据能够得到安全保护和可靠备份，以便我不会因为技术故障而丢失重要的创作成果。

#### Acceptance Criteria

1. WHEN 系统存储数据 THEN 应对敏感数据进行加密保护
2. WHEN 用户进行API调用 THEN 系统应验证请求的安全性和合法性
3. WHEN 系统处理用户输入 THEN 应进行输入验证和过滤防止注入攻击
4. WHEN 系统运行 THEN 应定期进行数据备份和完整性检查
5. WHEN 发生系统故障 THEN 应能够从备份中快速恢复数据

### Requirement 12: 多平台部署与扩展性

**User Story:** 作为系统管理员，我希望系统能够支持多平台部署并具备良好的扩展性，以便能够适应不同的使用场景和规模需求。

#### Acceptance Criteria

1. WHEN 部署系统 THEN 应支持Docker Compose一键部署多服务架构
2. WHEN 系统负载增加 THEN 应能够水平扩展处理能力
3. WHEN 需要集成新功能 THEN 系统架构应支持模块化扩展
4. WHEN 系统运行异常 THEN 应提供完整的监控告警和日志记录
5. WHEN 需要维护升级 THEN 应支持零停机时间的滚动更新