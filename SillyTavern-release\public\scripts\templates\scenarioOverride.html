<div class="scenario_override range-block flexFlowColumn flex-container">
    <div class="range-block-title title_restorable">
        <h3><span data-i18n="Chat Scenario Override" class="margin0">Chat Scenario Override</span></h3>
        <div title="Remove" data-i18n="[title]Remove"
            class="menu_button fa-solid fa-trash-can remove_scenario_override"></div>
    </div>
    <div class="range-block-counter justifyLeft flex-container flexFlowColumn">
        <strong data-i18n="Unique to this chat.">Unique to this chat.</strong>
        <span data-group="true" data-i18n="All group members will use the following scenario text instead of what is specified in their character cards.">All group members will use the following scenario text instead of what is specified in their character cards.</span>
        <span data-character="true" data-i18n="The following scenario text will be used instead of the value set in the character card.">The following scenario text will be used instead of the value set in the character card.</span>
        <span data-i18n="Checkpoints inherit the scenario override from their parent, and can be changed individually after that.">Checkpoints inherit the scenario override from their parent, and can be changed individually after that.</span>
    </div>
    <div class="range-block-range wide100p">
        <textarea class="wide100p chat_scenario" class="text_pole" rows="15" data-i18n="[placeholder]Type here..."
            placeholder="Type here..."></textarea>
    </div>
</div>
