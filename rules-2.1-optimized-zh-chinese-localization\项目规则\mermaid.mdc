---
description: "Mermaid diagram generation for various visualizations - Mermaid图表生成工具"
globs: ["**/*.md", "**/*.mdx", "**/*.mmd"]
alwaysApply: false
---

# 📊 Mermaid Diagrams - Mermaid图表

使用Mermaid创建各种类型的图表和可视化。

## 🚀 Commands - 命令

- `/mermaid` - 创建Mermaid图表
- `/flowchart` - 创建流程图
- `/sequence` - 创建时序图
- `/class-diagram` - 创建类图
- `/er-diagram` - 创建实体关系图

## 📋 Diagram Types - 图表类型

### 1. 🔄 流程图 (Flowchart)
```mermaid
flowchart TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E
```

### 2. ⏱️ 时序图 (Sequence Diagram)
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 登录请求
    F->>B: 验证用户
    B->>D: 查询用户信息
    D-->>B: 返回用户数据
    B-->>F: 返回认证结果
    F-->>U: 显示登录状态
```

### 3. 🏗️ 类图 (Class Diagram)
```mermaid
classDiagram
    class User {
        +String name
        +String email
        +login()
        +logout()
    }
    
    class Admin {
        +String permissions
        +manageUsers()
    }
    
    User <|-- Admin
```

### 4. 🗄️ 实体关系图 (ER Diagram)
```mermaid
erDiagram
    USER ||--o{ ORDER : places
    ORDER ||--|{ ORDER_ITEM : contains
    PRODUCT ||--o{ ORDER_ITEM : "ordered in"
    
    USER {
        int id PK
        string name
        string email
    }
    
    ORDER {
        int id PK
        int user_id FK
        date created_at
    }
```

### 5. 🌳 Git图 (Git Graph)
```mermaid
gitgraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    commit id: "Feature B"
    checkout main
    merge develop
    commit id: "Release v1.0"
```

### 6. 📊 甘特图 (Gantt Chart)
```mermaid
gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求调研    :done, des1, 2024-01-01, 2024-01-05
    需求文档    :done, des2, after des1, 3d
    section 开发阶段
    前端开发    :active, dev1, 2024-01-08, 10d
    后端开发    :dev2, 2024-01-08, 12d
    section 测试阶段
    单元测试    :test1, after dev1, 3d
    集成测试    :test2, after dev2, 2d
```

### 7. 🍰 饼图 (Pie Chart)
```mermaid
pie title 技术栈分布
    "Vue.js" : 35
    "React" : 30
    "Angular" : 15
    "其他" : 20
```

### 8. 📈 用户旅程图 (User Journey)
```mermaid
journey
    title 用户购物体验
    section 发现产品
      浏览首页: 5: 用户
      搜索产品: 3: 用户
      查看详情: 4: 用户
    section 购买决策
      比较价格: 2: 用户
      查看评价: 4: 用户
      添加购物车: 5: 用户
    section 完成购买
      结算支付: 3: 用户
      确认订单: 5: 用户
```

## 🛠️ Common Patterns - 常用模式

### 前端架构图
```mermaid
graph TB
    subgraph "前端层"
        A[Vue/React组件]
        B[状态管理]
        C[路由管理]
    end
    
    subgraph "服务层"
        D[API服务]
        E[工具函数]
        F[常量配置]
    end
    
    subgraph "后端层"
        G[REST API]
        H[GraphQL]
        I[WebSocket]
    end
    
    A --> B
    A --> C
    A --> D
    D --> G
    D --> H
    D --> I
```

### 数据流图
```mermaid
flowchart LR
    A[用户输入] --> B[表单验证]
    B --> C{验证通过?}
    C -->|是| D[发送请求]
    C -->|否| E[显示错误]
    D --> F[API处理]
    F --> G[数据库操作]
    G --> H[返回结果]
    H --> I[更新界面]
    E --> A
```

### 组件关系图
```mermaid
graph TD
    A[App.vue] --> B[Header.vue]
    A --> C[Main.vue]
    A --> D[Footer.vue]
    
    C --> E[Sidebar.vue]
    C --> F[Content.vue]
    
    F --> G[UserList.vue]
    F --> H[UserDetail.vue]
    
    G --> I[UserCard.vue]
    H --> J[UserForm.vue]
```

## ✅ Best Practices - 最佳实践

### 设计原则
- **简洁明了** - 避免过于复杂的图表
- **层次清晰** - 合理的信息层级
- **色彩协调** - 统一的配色方案
- **标注完整** - 清晰的标签和说明

### 使用场景
- **架构设计** - 系统架构和模块关系
- **流程说明** - 业务流程和操作步骤
- **数据建模** - 数据结构和关系
- **项目管理** - 进度计划和里程碑

### 维护策略
- **版本控制** - 图表文件纳入版本管理
- **文档同步** - 与代码保持同步更新
- **格式统一** - 统一的图表风格
- **定期审查** - 定期检查图表准确性

## 🔧 Integration - 集成

### Markdown支持
```markdown
# 系统架构

以下是系统的整体架构：

\`\`\`mermaid
graph TB
    A[前端] --> B[API网关]
    B --> C[微服务]
    C --> D[数据库]
\`\`\`
```

### 文档工具集成
- **VitePress** - 原生支持Mermaid
- **Docusaurus** - 插件支持
- **GitBook** - 内置支持
- **Notion** - 代码块支持

### 导出格式
- **SVG** - 矢量图形
- **PNG** - 位图格式
- **PDF** - 文档格式
- **HTML** - 网页嵌入

## 📋 Templates - 模板

### 系统设计模板
```mermaid
graph TB
    subgraph "客户端"
        A[Web应用]
        B[移动应用]
    end
    
    subgraph "服务端"
        C[负载均衡]
        D[应用服务器]
        E[数据库]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
```

### API设计模板
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant S as 服务
    participant D as 数据库
    
    C->>G: HTTP请求
    G->>S: 转发请求
    S->>D: 查询数据
    D-->>S: 返回数据
    S-->>G: 处理结果
    G-->>C: HTTP响应
```

## 📋 Checklist - 检查清单

### 图表设计
- [ ] 目的明确，信息准确
- [ ] 结构清晰，层次分明
- [ ] 标注完整，易于理解
- [ ] 风格统一，美观大方

### 技术实现
- [ ] 语法正确，渲染正常
- [ ] 兼容性良好，跨平台支持
- [ ] 性能优化，加载快速
- [ ] 维护方便，易于更新

### 文档集成
- [ ] 与文档系统集成
- [ ] 版本控制管理
- [ ] 自动化生成
- [ ] 定期更新维护
