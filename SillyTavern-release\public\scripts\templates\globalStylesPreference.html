<div class="flex-container flexFlowColumn">
    <h3 data-i18n="Choose how to apply CSS style tags if they are defined in Creator's Notes of this character:" class="margin0">
        Choose how to apply CSS style tags if they are defined in Creator's Notes of this character:
    </h3>
    <h4 data-i18n="CAUTION: Malformed styles may cause issues." class="neutral_warning">
        CAUTION: Malformed styles may cause issues.
    </h4>
    <label class="checkbox_label" for="global_styles_forbidden">
        <input type="radio" id="global_styles_forbidden" name="global_styles_preference" />
        <span data-i18n="Just to Creator's Notes">Just to Creator's Notes</span>
    </label>
    <label class="checkbox_label" for="global_styles_allowed">
        <input type="radio" id="global_styles_allowed" name="global_styles_preference" />
        <span data-i18n="Apply to the entire app">Apply to the entire app</span>
    </label>
</div>
