{"enabled": true, "name": "Localization Translation Monitor", "description": "Monitors changes to user-facing text content files (JSON, YAML, and other localization files) and automatically generates translations for all configured target languages while maintaining proper context and locale-specific conventions", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.json", "**/*.yaml", "**/*.yml", "**/locales/**/*", "**/i18n/**/*", "**/translations/**/*", "**/lang/**/*"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes to identify new or modified text content that requires translation. For any new or changed text:\n\n1. Identify the source language and extract the new/modified text entries\n2. Determine the target languages that need translations (check project configuration or ask user)\n3. Generate accurate translations that:\n   - Maintain the original meaning and context\n   - Follow locale-specific conventions (date formats, number formats, cultural adaptations)\n   - Preserve any formatting, placeholders, or interpolation syntax\n   - Consider the UI context where the text will be displayed\n4. Update the corresponding translation files for each target language\n5. Ensure consistency with existing translations in the project\n6. Validate that all translation keys are properly structured and accessible\n\nPlease provide the translations and update the relevant files accordingly."}}