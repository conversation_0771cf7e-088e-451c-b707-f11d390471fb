# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建产物
dist
build

# 版本控制
.git
.gitignore

# IDE和编辑器
.vscode
.idea
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log

# 临时文件
.tmp
.temp

# 测试覆盖率
coverage

# Docker相关
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
*.md

# 许可证
LICENSE 